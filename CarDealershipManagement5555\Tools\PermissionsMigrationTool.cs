using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace CarDealershipManagement.Tools
{
    /// <summary>
    /// أداة ترحيل الصلاحيات - لتحديث النظام الحالي مع النظام الجديد للصلاحيات
    /// </summary>
    public static class PermissionsMigrationTool
    {
        /// <summary>
        /// تحديث جميع المستخدمين الموجودين مع الصلاحيات الجديدة
        /// </summary>
        public static async Task<bool> MigrateExistingUsersPermissions()
        {
            try
            {
                Console.WriteLine("🔄 بدء ترحيل الصلاحيات للمستخدمين الموجودين...");
                
                using var context = DbContextFactory.CreateContext();
                
                // الحصول على جميع المستخدمين مع صلاحياتهم
                var users = await context.Users
                    .Include(u => u.Permissions)
                    .ToListAsync();

                int updatedCount = 0;
                int errorCount = 0;

                foreach (var user in users)
                {
                    try
                    {
                        Console.WriteLine($"📝 تحديث صلاحيات المستخدم: {user.Username} ({user.Role})");
                        
                        // إنشاء صلاحيات جديدة بناءً على الدور
                        var newPermissions = PermissionService.GetDefaultPermissionsForRole(user.Role);
                        
                        if (user.Permissions == null)
                        {
                            // إنشاء صلاحيات جديدة
                            newPermissions.UserId = user.UserId;
                            context.UserPermissions.Add(newPermissions);
                            Console.WriteLine($"   ✅ تم إنشاء صلاحيات جديدة للمستخدم {user.Username}");
                        }
                        else
                        {
                            // تحديث الصلاحيات الموجودة مع الحفاظ على التخصيصات
                            await UpdateExistingPermissions(user.Permissions, newPermissions, user.Role);
                            Console.WriteLine($"   ✅ تم تحديث صلاحيات المستخدم {user.Username}");
                        }
                        
                        updatedCount++;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ خطأ في تحديث المستخدم {user.Username}: {ex.Message}");
                        errorCount++;
                    }
                }

                // حفظ التغييرات
                await context.SaveChangesAsync();
                
                Console.WriteLine($"\n📊 نتائج الترحيل:");
                Console.WriteLine($"   ✅ تم تحديث {updatedCount} مستخدم بنجاح");
                if (errorCount > 0)
                {
                    Console.WriteLine($"   ❌ فشل في تحديث {errorCount} مستخدم");
                }
                
                Console.WriteLine("🎉 تم الانتهاء من ترحيل الصلاحيات بنجاح!");
                return errorCount == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ عام في ترحيل الصلاحيات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث الصلاحيات الموجودة مع الحفاظ على التخصيصات
        /// </summary>
        private static async Task UpdateExistingPermissions(UserPermissions existing, UserPermissions newDefaults, UserRole userRole)
        {
            // نسخ الصلاحيات الافتراضية الجديدة
            foreach (var permission in PermissionService.AllPermissions)
            {
                var permissionName = permission.Key;
                var property = typeof(UserPermissions).GetProperty(permissionName);
                
                if (property != null)
                {
                    var newValue = property.GetValue(newDefaults) as bool?;
                    var currentValue = property.GetValue(existing) as bool?;
                    
                    // إذا كانت الصلاحية الحالية false والجديدة true، نحدثها
                    // إذا كانت الصلاحية الحالية true، نتركها كما هي (تخصيص المستخدم)
                    if (newValue == true && currentValue == false)
                    {
                        property.SetValue(existing, true);
                    }
                }
            }

            // تحديث الصلاحيات الخاصة بكل دور
            UpdateRoleSpecificPermissions(existing, userRole);
            
            // تحديث تاريخ التعديل
            existing.ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تحديث الصلاحيات الخاصة بكل دور
        /// </summary>
        private static void UpdateRoleSpecificPermissions(UserPermissions permissions, UserRole role)
        {
            switch (role)
            {
                case UserRole.Developer:
                    // المطور له جميع الصلاحيات الخاصة
                    permissions.CanAddManager = true;
                    permissions.CanManageManagerPassword = true;
                    permissions.CanActivateSubscription = true;
                    permissions.CanActivateInstallation = true;
                    permissions.CanResetSystem = true;
                    permissions.CanRestoreDefaults = true;
                    break;

                case UserRole.Manager:
                    // المدير له صلاحيات إدارة النشاط
                    permissions.CanFullActivityManagement = true;
                    permissions.CanCopyDatabase = true;
                    permissions.CanArchiveSystem = true;
                    permissions.CanAddSalesRep = true;
                    permissions.CanManageSalesRepPassword = true;
                    // المدير لا يدير المستخدمين
                    permissions.CanManageUsers = false;
                    break;

                case UserRole.SalesRepresentative:
                    // المندوب ليس له صلاحيات خاصة إضافية
                    // إزالة الصلاحيات الإدارية إذا كانت موجودة
                    permissions.CanManageUsers = false;
                    permissions.CanAddManager = false;
                    permissions.CanManageManagerPassword = false;
                    permissions.CanActivateSubscription = false;
                    permissions.CanActivateInstallation = false;
                    permissions.CanResetSystem = false;
                    permissions.CanRestoreDefaults = false;
                    permissions.CanFullActivityManagement = false;
                    permissions.CanCopyDatabase = false;
                    permissions.CanArchiveSystem = false;
                    permissions.CanAddSalesRep = false;
                    permissions.CanManageSalesRepPassword = false;
                    break;
            }
        }

        /// <summary>
        /// إنشاء تقرير عن الصلاحيات الحالية
        /// </summary>
        public static async Task<string> GeneratePermissionsReport()
        {
            try
            {
                using var context = DbContextFactory.CreateContext();
                
                var users = await context.Users
                    .Include(u => u.Permissions)
                    .OrderBy(u => u.Role)
                    .ThenBy(u => u.Username)
                    .ToListAsync();

                var report = "📊 تقرير الصلاحيات الحالية\n";
                report += "=" + new string('=', 50) + "\n\n";

                foreach (var role in Enum.GetValues<UserRole>())
                {
                    var roleUsers = users.Where(u => u.Role == role).ToList();
                    if (!roleUsers.Any()) continue;

                    var roleName = role switch
                    {
                        UserRole.Developer => "🔧 المطورين",
                        UserRole.Manager => "👔 المديرين", 
                        UserRole.SalesRepresentative => "🤝 مندوبي المبيعات",
                        _ => "غير محدد"
                    };

                    report += $"{roleName} ({roleUsers.Count}):\n";
                    report += new string('-', 30) + "\n";

                    foreach (var user in roleUsers)
                    {
                        report += $"• {user.Username} ({user.FullName}) - ";
                        report += user.IsActive ? "نشط" : "غير نشط";
                        
                        if (user.Permissions != null)
                        {
                            var enabledCount = CountEnabledPermissions(user.Permissions);
                            var totalCount = PermissionService.AllPermissions.Count;
                            report += $" - الصلاحيات: {enabledCount}/{totalCount}";
                        }
                        else
                        {
                            report += " - لا توجد صلاحيات";
                        }
                        
                        report += "\n";
                    }
                    report += "\n";
                }

                return report;
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في إنشاء التقرير: {ex.Message}";
            }
        }

        /// <summary>
        /// عد الصلاحيات المفعلة
        /// </summary>
        private static int CountEnabledPermissions(UserPermissions permissions)
        {
            int count = 0;
            
            foreach (var permission in PermissionService.AllPermissions.Keys)
            {
                var property = typeof(UserPermissions).GetProperty(permission);
                if (property != null)
                {
                    var value = property.GetValue(permissions) as bool?;
                    if (value == true) count++;
                }
            }
            
            return count;
        }

        /// <summary>
        /// التحقق من صحة النظام الجديد
        /// </summary>
        public static async Task<bool> ValidateNewPermissionSystem()
        {
            try
            {
                Console.WriteLine("🔍 التحقق من صحة النظام الجديد للصلاحيات...");
                
                // التحقق من وجود جميع الصلاحيات في النموذج
                var permissionProperties = typeof(UserPermissions).GetProperties()
                    .Where(p => p.PropertyType == typeof(bool))
                    .Select(p => p.Name)
                    .ToList();

                var definedPermissions = PermissionService.AllPermissions.Keys.ToList();
                
                var missingInService = permissionProperties.Except(definedPermissions).ToList();
                var missingInModel = definedPermissions.Except(permissionProperties).ToList();

                if (missingInService.Any())
                {
                    Console.WriteLine($"⚠️ صلاحيات موجودة في النموذج لكن غير معرفة في الخدمة: {string.Join(", ", missingInService)}");
                }

                if (missingInModel.Any())
                {
                    Console.WriteLine($"⚠️ صلاحيات معرفة في الخدمة لكن غير موجودة في النموذج: {string.Join(", ", missingInModel)}");
                }

                // التحقق من الصلاحيات الافتراضية لكل دور
                foreach (var role in Enum.GetValues<UserRole>())
                {
                    var permissions = PermissionService.GetDefaultPermissionsForRole(role);
                    Console.WriteLine($"✅ تم التحقق من الصلاحيات الافتراضية للدور: {role}");
                }

                bool isValid = !missingInService.Any() && !missingInModel.Any();
                
                if (isValid)
                {
                    Console.WriteLine("✅ النظام الجديد للصلاحيات صحيح ومتكامل!");
                }
                else
                {
                    Console.WriteLine("❌ يوجد مشاكل في النظام الجديد للصلاحيات!");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في التحقق من النظام: {ex.Message}");
                return false;
            }
        }
    }
}
