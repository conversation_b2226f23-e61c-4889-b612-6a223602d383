using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class SettingsForm : Form
    {
        private TabControl tabControl;
        private TabPage tabCompanyInfo;
        private TabPage tabSystemSettings;
        private TabPage tabBackupSettings;

        // Company Info Tab
        private TextBox txtCompanyName;
        private TextBox txtCompanyAddress;
        private TextBox txtCompanyPhone;
        private TextBox txtCommercialRegister;
        private TextBox txtTaxCard;
        private ComboBox cmbCurrency;
        private PictureBox picCompanyLogo;
        private Button btnUploadLogo;
        private Button btnRemoveLogo;
        private Label lblLogoPath;
        private Button btnSaveCompanyInfo;
        private Label lblCompanyInfoStatus;

        // System Settings Tab
        private ComboBox cmbSubscriptionType;
        private DateTimePicker dtpSubscriptionStart;
        private DateTimePicker dtpSubscriptionEnd;
        private CheckBox chkIsActive;
        private Button btnSaveSystemSettings;
        private Label lblSystemSettingsStatus;

        // Backup Settings Tab
        private CheckBox chkAutoBackupEnabled;
        private NumericUpDown nudBackupInterval;
        private TextBox txtBackupPath;
        private Button btnBrowseBackupPath;
        private Button btnCreateBackup;
        private Button btnRestoreBackup;
        private Button btnSaveBackupSettings;
        private Label lblBackupSettingsStatus;

        private SystemSettings? currentSettings;

        public SettingsForm()
        {
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "إعدادات النظام - System Settings";
            this.Size = new Size(600, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Create tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                SelectedTab = tabCompanyInfo
            };

            // Company Info Tab
            tabCompanyInfo = new TabPage("معلومات الشركة");
            SetupCompanyInfoTab();

            // System Settings Tab
            tabSystemSettings = new TabPage("إعدادات النظام");
            SetupSystemSettingsTab();

            // Backup Settings Tab
            tabBackupSettings = new TabPage("إعدادات النسخ الاحتياطي");
            SetupBackupSettingsTab();

            tabControl.TabPages.AddRange(new TabPage[]
            {
                tabCompanyInfo, tabSystemSettings, tabBackupSettings
            });
            this.Controls.Add(tabControl);
        }

        private void SetupCompanyInfoTab()
        {
            int yPos = 30;
            int spacing = 40;

            // Status Label
            lblCompanyInfoStatus = new Label
            {
                Text = "",
                Location = new Point(30, 500),
                Size = new Size(540, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Company Name
            var lblCompanyName = new Label { Text = "اسم الشركة:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            txtCompanyName = new TextBox { Location = new Point(140, yPos), Size = new Size(300, 23) };

            // Company Address
            yPos += spacing;
            var lblCompanyAddress = new Label { Text = "عنوان الشركة:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            txtCompanyAddress = new TextBox { Location = new Point(140, yPos), Size = new Size(300, 23) };

            // Company Phone
            yPos += spacing;
            var lblCompanyPhone = new Label { Text = "هاتف الشركة:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            txtCompanyPhone = new TextBox { Location = new Point(140, yPos), Size = new Size(300, 23) };

            // Commercial Register
            yPos += spacing;
            var lblCommercialRegister = new Label { Text = "السجل التجاري:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            txtCommercialRegister = new TextBox { Location = new Point(140, yPos), Size = new Size(300, 23) };

            // Tax Card
            yPos += spacing;
            var lblTaxCard = new Label { Text = "البطاقة الضريبية:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            txtTaxCard = new TextBox { Location = new Point(140, yPos), Size = new Size(300, 23) };

            // Currency
            yPos += spacing;
            var lblCurrency = new Label { Text = "العملة:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            cmbCurrency = new ComboBox { Location = new Point(140, yPos), Size = new Size(150, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            cmbCurrency.Items.AddRange(new[] { "USD", "EUR", "SAR", "AED", "JOD", "EGP" });

            // Company Logo
            yPos += spacing;
            var lblCompanyLogo = new Label { Text = "شعار الشركة:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            picCompanyLogo = new PictureBox
            {
                Location = new Point(140, yPos),
                Size = new Size(120, 80),
                SizeMode = PictureBoxSizeMode.Zoom,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightGray
            };

            btnUploadLogo = new Button { Text = "رفع الشعار", Location = new Point(270, yPos), Size = new Size(80, 30) };
            btnRemoveLogo = new Button { Text = "إزالة", Location = new Point(360, yPos), Size = new Size(60, 30) };
            btnUploadLogo.Click += BtnUploadLogo_Click;
            btnRemoveLogo.Click += BtnRemoveLogo_Click;

            yPos += 85;
            lblLogoPath = new Label
            {
                Text = "لا يوجد شعار",
                Location = new Point(140, yPos),
                Size = new Size(300, 20),
                ForeColor = Color.Gray,
                Font = new Font("Segoe UI", 8)
            };

            // Save button
            yPos += spacing;
            btnSaveCompanyInfo = new Button
            {
                Text = "حفظ معلومات الشركة",
                Location = new Point(140, yPos),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnSaveCompanyInfo.FlatAppearance.BorderSize = 0;
            btnSaveCompanyInfo.Click += BtnSaveCompanyInfo_Click;

            tabCompanyInfo.Controls.AddRange(new Control[]
            {
                lblCompanyName, txtCompanyName, lblCompanyAddress, txtCompanyAddress,
                lblCompanyPhone, txtCompanyPhone, lblCommercialRegister, txtCommercialRegister,
                lblTaxCard, txtTaxCard, lblCurrency, cmbCurrency, lblCompanyLogo,
                picCompanyLogo, btnUploadLogo, btnRemoveLogo, lblLogoPath, btnSaveCompanyInfo, lblCompanyInfoStatus
            });
        }

        private void SetupSystemSettingsTab()
        {
            int yPos = 30;
            int spacing = 40;

            // Status Label
            lblSystemSettingsStatus = new Label
            {
                Text = "",
                Location = new Point(30, 500),
                Size = new Size(540, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Subscription Type
            var lblSubscriptionType = new Label { Text = "نوع الاشتراك:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            cmbSubscriptionType = new ComboBox { Location = new Point(140, yPos), Size = new Size(150, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            cmbSubscriptionType.Items.AddRange(new[] { "شهري", "ربع سنوي", "نصف سنوي", "سنوي" });

            // Subscription Start Date
            yPos += spacing;
            var lblSubscriptionStart = new Label { Text = "تاريخ بداية الاشتراك:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            dtpSubscriptionStart = new DateTimePicker { Location = new Point(140, yPos), Size = new Size(200, 23) };

            // Subscription End Date
            yPos += spacing;
            var lblSubscriptionEnd = new Label { Text = "تاريخ انتهاء الاشتراك:", Location = new Point(30, yPos), Size = new Size(100, 23) };
            dtpSubscriptionEnd = new DateTimePicker { Location = new Point(140, yPos), Size = new Size(200, 23) };

            // Is Active
            yPos += spacing;
            chkIsActive = new CheckBox { Text = "الاشتراك نشط", Location = new Point(140, yPos), Size = new Size(150, 23) };

            // Subscription info
            yPos += spacing;
            var lblSubscriptionInfo = new Label
            {
                Text = "معلومات الاشتراك:",
                Location = new Point(30, yPos),
                Size = new Size(400, 60),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            // Save button
            yPos += 80;
            btnSaveSystemSettings = new Button
            {
                Text = "حفظ إعدادات النظام",
                Location = new Point(140, yPos),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnSaveSystemSettings.FlatAppearance.BorderSize = 0;
            btnSaveSystemSettings.Click += BtnSaveSystemSettings_Click;

            tabSystemSettings.Controls.AddRange(new Control[]
            {
                lblSubscriptionType, cmbSubscriptionType, lblSubscriptionStart, dtpSubscriptionStart,
                lblSubscriptionEnd, dtpSubscriptionEnd, chkIsActive, lblSubscriptionInfo, btnSaveSystemSettings, lblSystemSettingsStatus
            });
        }

        private void SetupBackupSettingsTab()
        {
            int yPos = 30;
            int spacing = 40;

            // Status Label
            lblBackupSettingsStatus = new Label
            {
                Text = "",
                Location = new Point(30, 500),
                Size = new Size(540, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Auto Backup Enabled
            chkAutoBackupEnabled = new CheckBox { Text = "تفعيل النسخ الاحتياطي التلقائي", Location = new Point(30, yPos), Size = new Size(200, 23) };

            // Backup Interval
            yPos += spacing;
            var lblBackupInterval = new Label { Text = "فترة النسخ (بالساعات):", Location = new Point(30, yPos), Size = new Size(130, 23) };
            nudBackupInterval = new NumericUpDown
            {
                Location = new Point(170, yPos),
                Size = new Size(100, 23),
                Minimum = 1,
                Maximum = 168, // 1 week
                Value = 24
            };

            // Backup Path
            yPos += spacing;
            var lblBackupPath = new Label { Text = "مسار النسخ الاحتياطي:", Location = new Point(30, yPos), Size = new Size(130, 23) };
            txtBackupPath = new TextBox { Location = new Point(170, yPos), Size = new Size(250, 23) };
            btnBrowseBackupPath = new Button { Text = "استعراض", Location = new Point(430, yPos), Size = new Size(80, 25) };
            btnBrowseBackupPath.Click += BtnBrowseBackupPath_Click;

            // Action buttons
            yPos += spacing + 20;
            btnCreateBackup = new Button
            {
                Text = "إنشاء نسخة احتياطية",
                Location = new Point(30, yPos),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnCreateBackup.FlatAppearance.BorderSize = 0;
            btnCreateBackup.Click += BtnCreateBackup_Click;

            btnRestoreBackup = new Button
            {
                Text = "استعادة نسخة احتياطية",
                Location = new Point(190, yPos),
                Size = new Size(150, 35),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnRestoreBackup.FlatAppearance.BorderSize = 0;
            btnRestoreBackup.Click += BtnRestoreBackup_Click;

            // Save button
            yPos += spacing;
            btnSaveBackupSettings = new Button
            {
                Text = "حفظ إعدادات النسخ الاحتياطي",
                Location = new Point(30, yPos),
                Size = new Size(180, 35),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnSaveBackupSettings.FlatAppearance.BorderSize = 0;
            btnSaveBackupSettings.Click += BtnSaveBackupSettings_Click;

            tabBackupSettings.Controls.AddRange(new Control[]
            {
                chkAutoBackupEnabled, lblBackupInterval, nudBackupInterval,
                lblBackupPath, txtBackupPath, btnBrowseBackupPath,
                btnCreateBackup, btnRestoreBackup, btnSaveBackupSettings, lblBackupSettingsStatus
            });
        }

        private async void LoadSettings()
        {
            lblCompanyInfoStatus.Text = "";
            lblSystemSettingsStatus.Text = "";
            lblBackupSettingsStatus.Text = "";

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                currentSettings = await context.SystemSettings.FirstOrDefaultAsync();

                if (currentSettings != null)
                {
                    // Load company info
                    txtCompanyName.Text = currentSettings.CompanyName ?? "";
                    txtCompanyAddress.Text = currentSettings.CompanyAddress ?? "";
                    txtCompanyPhone.Text = currentSettings.CompanyPhone ?? "";
                    txtCommercialRegister.Text = currentSettings.CommercialRegister ?? "";
                    txtTaxCard.Text = currentSettings.TaxCard ?? "";
                    cmbCurrency.Text = currentSettings.Currency ?? "USD";

                    // Load company logo
                    if (!string.IsNullOrEmpty(currentSettings.CompanyLogo))
                    {
                        string logoPath = Path.Combine(Application.StartupPath, currentSettings.CompanyLogo);
                        LoadLogoImage(logoPath);
                    }

                    // Load system settings
                    cmbSubscriptionType.SelectedIndex = (int)currentSettings.SubscriptionType;
                    dtpSubscriptionStart.Value = currentSettings.SubscriptionStartDate ?? DateTime.Now;
                    dtpSubscriptionEnd.Value = currentSettings.SubscriptionEndDate ?? DateTime.Now.AddYears(1);
                    chkIsActive.Checked = currentSettings.IsActive;

                    // Load backup settings
                    chkAutoBackupEnabled.Checked = currentSettings.AutoBackupEnabled;
                    nudBackupInterval.Value = currentSettings.BackupIntervalHours;
                    txtBackupPath.Text = currentSettings.BackupPath ?? "";
                }
                else
                {
                    // Create default settings if none exist
                    currentSettings = new SystemSettings
                    {
                        SubscriptionType = SubscriptionType.Yearly,
                        SubscriptionStartDate = DateTime.Now,
                        SubscriptionEndDate = DateTime.Now.AddYears(1),
                        IsActive = true,
                        Currency = "USD",
                        CompanyName = "معرض السيارات",
                        CompanyAddress = "",
                        CompanyPhone = "",
                        AutoBackupEnabled = false,
                        BackupIntervalHours = 24,
                        BackupPath = "",
                        CreatedDate = DateTime.Now
                    };

                    context.SystemSettings.Add(currentSettings);
                    await context.SaveChangesAsync();
                    LoadSettings(); // Reload to get the ID
                }
            }, "تحميل الإعدادات");
        }

        private async void BtnSaveCompanyInfo_Click(object? sender, EventArgs e)
        {
            lblCompanyInfoStatus.Text = "";
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var settings = await context.SystemSettings.FirstOrDefaultAsync();
                if (settings == null)
                {
                    // Create new settings if none exist
                    settings = new SystemSettings
                    {
                        SubscriptionType = SubscriptionType.Yearly,
                        SubscriptionStartDate = DateTime.Now,
                        SubscriptionEndDate = DateTime.Now.AddYears(1),
                        IsActive = true,
                        Currency = "USD",
                        CompanyName = "معرض السيارات",
                        CompanyAddress = "",
                        CompanyPhone = "",
                        AutoBackupEnabled = false,
                        BackupIntervalHours = 24,
                        BackupPath = "",
                        CreatedDate = DateTime.Now
                    };
                    context.SystemSettings.Add(settings);
                }

                // Update company info
                settings.CompanyName = txtCompanyName.Text.Trim();
                settings.CompanyAddress = txtCompanyAddress.Text.Trim();
                settings.CompanyPhone = txtCompanyPhone.Text.Trim();
                settings.CommercialRegister = txtCommercialRegister.Text.Trim();
                settings.TaxCard = txtTaxCard.Text.Trim();
                settings.Currency = cmbCurrency.Text;
                settings.ModifiedDate = DateTime.Now;

                // Update logo path if currentSettings has been modified
                if (currentSettings != null && !string.IsNullOrEmpty(currentSettings.CompanyLogo))
                {
                    settings.CompanyLogo = currentSettings.CompanyLogo;
                }

                await context.SaveChangesAsync();
                
                // Update currentSettings to reflect the changes
                currentSettings = settings;
                
                lblCompanyInfoStatus.Text = "تم حفظ معلومات الشركة بنجاح.";
                lblCompanyInfoStatus.ForeColor = Color.Green;
            }, "حفظ معلومات الشركة");
        }

        private async void BtnSaveSystemSettings_Click(object? sender, EventArgs e)
        {
            lblSystemSettingsStatus.Text = "";
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var settings = await context.SystemSettings.FirstOrDefaultAsync();
                if (settings == null)
                {
                    lblSystemSettingsStatus.Text = "يجب إنشاء الإعدادات أولاً من تبويب معلومات الشركة.";
                    lblSystemSettingsStatus.ForeColor = Color.Orange;
                    return;
                }

                settings.SubscriptionType = (SubscriptionType)cmbSubscriptionType.SelectedIndex;
                settings.SubscriptionStartDate = dtpSubscriptionStart.Value;
                settings.SubscriptionEndDate = dtpSubscriptionEnd.Value;
                settings.IsActive = chkIsActive.Checked;
                settings.ModifiedDate = DateTime.Now;

                await context.SaveChangesAsync();
                
                // Update currentSettings to reflect the changes
                currentSettings = settings;
                
                lblSystemSettingsStatus.Text = "تم حفظ إعدادات النظام بنجاح.";
                lblSystemSettingsStatus.ForeColor = Color.Green;
            }, "حفظ إعدادات النظام");
        }

        private async void BtnSaveBackupSettings_Click(object? sender, EventArgs e)
        {
            lblBackupSettingsStatus.Text = "";
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var settings = await context.SystemSettings.FirstOrDefaultAsync();
                if (settings == null)
                {
                    lblBackupSettingsStatus.Text = "يجب إنشاء الإعدادات أولاً من تبويب معلومات الشركة.";
                    lblBackupSettingsStatus.ForeColor = Color.Orange;
                    return;
                }

                settings.AutoBackupEnabled = chkAutoBackupEnabled.Checked;
                settings.BackupIntervalHours = (int)nudBackupInterval.Value;
                settings.BackupPath = txtBackupPath.Text.Trim();
                settings.ModifiedDate = DateTime.Now;

                await context.SaveChangesAsync();
                
                // Update currentSettings to reflect the changes
                currentSettings = settings;
                
                lblBackupSettingsStatus.Text = "تم حفظ إعدادات النسخ الاحتياطي بنجاح.";
                lblBackupSettingsStatus.ForeColor = Color.Green;
            }, "حفظ إعدادات النسخ الاحتياطي");
        }

        private void BtnBrowseBackupPath_Click(object? sender, EventArgs e)
        {
            lblBackupSettingsStatus.Text = "";
            using var folderDialog = new FolderBrowserDialog();
            folderDialog.Description = "اختر مجلد النسخ الاحتياطي";

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                txtBackupPath.Text = folderDialog.SelectedPath;
            }
        }

        private async void BtnCreateBackup_Click(object? sender, EventArgs e)
        {
            lblBackupSettingsStatus.Text = "";
            ErrorHandlingService.TryExecute(() =>
            {
                string backupPath = string.IsNullOrWhiteSpace(txtBackupPath.Text)
                                    ? Path.Combine(Application.StartupPath, "Backups")
                                    : txtBackupPath.Text;

                if (!Directory.Exists(backupPath))
                {
                    Directory.CreateDirectory(backupPath);
                }

                string sourceDbPath = Path.Combine(Application.StartupPath, "CarDealership.db");
                string backupFileName = $"CarDealership_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                string destinationPath = Path.Combine(backupPath, backupFileName);

                if (File.Exists(sourceDbPath))
                {
                    File.Copy(sourceDbPath, destinationPath, true);
                    lblBackupSettingsStatus.Text = $"تم إنشاء النسخة الاحتياطية بنجاح في:\n{destinationPath}";
                    lblBackupSettingsStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblBackupSettingsStatus.Text = "لم يتم العثور على قاعدة البيانات.";
                    lblBackupSettingsStatus.ForeColor = Color.Red;
                }
            }, "إنشاء نسخة احتياطية");
        }

        private async void BtnRestoreBackup_Click(object? sender, EventArgs e)
        {
            lblBackupSettingsStatus.Text = "";
            using var openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Database files (*.db)|*.db|All files (*.*)|*.*";
            openFileDialog.Title = "اختر ملف النسخة الاحتياطية";

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                var result = MessageBox.Show(
                                 "هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية!",
                                 "تأكيد الاستعادة",
                                 MessageBoxButtons.YesNo,
                                 MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    ErrorHandlingService.TryExecute(() =>
                    {
                        string currentDbPath = Path.Combine(Application.StartupPath, "CarDealership.db");

                        // Create a backup of current database before restore
                        string tempBackup = Path.Combine(Application.StartupPath, $"CarDealership_BeforeRestore_{DateTime.Now:yyyyMMdd_HHmmss}.db");
                        if (File.Exists(currentDbPath))
                        {
                            File.Copy(currentDbPath, tempBackup, true);
                        }

                        // Restore the selected backup
                        File.Copy(openFileDialog.FileName, currentDbPath, true);

                        lblBackupSettingsStatus.Text = $"تم استعادة النسخة الاحتياطية بنجاح!\nتم حفظ نسخة من البيانات القديمة في: {tempBackup}";
                        lblBackupSettingsStatus.ForeColor = Color.Green;

                        // Suggest restarting the application
                        var restartResult = MessageBox.Show(
                                                "يُنصح بإعادة تشغيل التطبيق لضمان عمل النظام بشكل صحيح. هل تريد إغلاق التطبيق الآن؟",
                                                "إعادة التشغيل",
                                                MessageBoxButtons.YesNo,
                                                MessageBoxIcon.Question);

                        if (restartResult == DialogResult.Yes)
                        {
                            Application.Exit();
                        }
                    }, "استعادة نسخة احتياطية");
                }
            }
        }

        private async void BtnUploadLogo_Click(object? sender, EventArgs e)
        {
            lblCompanyInfoStatus.Text = "";
            using var openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "Image files (*.png;*.jpg;*.jpeg;*.bmp;*.gif)|*.png;*.jpg;*.jpeg;*.bmp;*.gif";
            openFileDialog.Title = "اختر شعار الشركة";

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                await ErrorHandlingService.TryExecute(async () =>
                {
                    // Create Resources directory if it doesn't exist
                    string resourcesPath = Path.Combine(Application.StartupPath, "Resources");
                    if (!Directory.Exists(resourcesPath))
                    {
                        Directory.CreateDirectory(resourcesPath);
                    }

                    // Get file extension
                    string extension = Path.GetExtension(openFileDialog.FileName);
                    string logoFileName = $"logo{extension}";
                    string logoPath = Path.Combine(resourcesPath, logoFileName);

                    // Copy the selected image to Resources folder
                    File.Copy(openFileDialog.FileName, logoPath, true);

                    // Load the image in the PictureBox
                    LoadLogoImage(logoPath);

                    // Update the settings
                    if (currentSettings != null)
                    {
                        currentSettings.CompanyLogo = $"Resources/{logoFileName}";
                        await SaveLogoPathToDatabase();
                    }

                    lblCompanyInfoStatus.Text = "تم رفع الشعار بنجاح.";
                    lblCompanyInfoStatus.ForeColor = Color.Green;
                }, "رفع الشعار");
            }
        }

        private async void BtnRemoveLogo_Click(object? sender, EventArgs e)
        {
            lblCompanyInfoStatus.Text = "";
            var result = MessageBox.Show(
                             "هل أنت متأكد من إزالة شعار الشركة؟",
                             "تأكيد الإزالة",
                             MessageBoxButtons.YesNo,
                             MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                await ErrorHandlingService.TryExecute(async () =>
                {
                    // Clear the PictureBox
                    picCompanyLogo.Image?.Dispose();
                    picCompanyLogo.Image = null;

                    // Update the label
                    lblLogoPath.Text = "لا يوجد شعار";
                    lblLogoPath.ForeColor = Color.Gray;

                    // Update the settings
                    if (currentSettings != null)
                    {
                        // Remove the logo file if it exists
                        if (!string.IsNullOrEmpty(currentSettings.CompanyLogo))
                        {
                            string logoPath = Path.Combine(Application.StartupPath, currentSettings.CompanyLogo);
                            if (File.Exists(logoPath))
                            {
                                File.Delete(logoPath);
                            }
                        }

                        currentSettings.CompanyLogo = null;
                        await SaveLogoPathToDatabase();
                    }

                    lblCompanyInfoStatus.Text = "تم إزالة الشعار بنجاح.";
                    lblCompanyInfoStatus.ForeColor = Color.Green;
                }, "إزالة الشعار");
            }
        }

        private void LoadLogoImage(string logoPath)
        {
            try
            {
                if (File.Exists(logoPath))
                {
                    // Dispose existing image if any
                    picCompanyLogo.Image?.Dispose();

                    // Load new image
                    using (var originalImage = Image.FromFile(logoPath))
                    {
                        // Create a copy to avoid file lock
                        picCompanyLogo.Image = new Bitmap(originalImage);
                    }

                    lblLogoPath.Text = Path.GetFileName(logoPath);
                    lblLogoPath.ForeColor = Color.Green;
                }
                else
                {
                    picCompanyLogo.Image = null;
                    lblLogoPath.Text = "لا يوجد شعار";
                    lblLogoPath.ForeColor = Color.Gray;
                }
            }
            catch (Exception ex)
            {
                picCompanyLogo.Image = null;
                lblLogoPath.Text = $"خطأ في تحميل الشعار: {ex.Message}";
                lblLogoPath.ForeColor = Color.Red;
            }
        }

        private async Task SaveLogoPathToDatabase()
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var settings = await context.SystemSettings.FirstOrDefaultAsync();
                if (settings != null)
                {
                    settings.CompanyLogo = currentSettings?.CompanyLogo;
                    settings.ModifiedDate = DateTime.Now;
                    await context.SaveChangesAsync();
                }
            }, "حفظ مسار الشعار في قاعدة البيانات");
        }
    }
}


