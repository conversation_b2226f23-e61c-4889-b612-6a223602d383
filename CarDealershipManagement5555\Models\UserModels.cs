using System.ComponentModel.DataAnnotations;

namespace CarDealershipManagement.Models
{
    public class User
    {
        [Key]
        public int UserId
        {
            get;
            set;
        }

        [Required(ErrorMessage = "اسم المستخدم مطلوب.")]
        [StringLength(50, ErrorMessage = "اسم المستخدم لا يمكن أن يتجاوز 50 حرفًا.")]
        public string Username
        {
            get;
            set;
        } = string.Empty; // اسم المستخدم

        [Required(ErrorMessage = "كلمة المرور مطلوبة.")]
        [StringLength(256, ErrorMessage = "كلمة المرور لا يمكن أن تتجاوز 256 حرفًا.")]
        public string PasswordHash
        {
            get;
            set;
        } = string.Empty; // كلمة المرور مشفرة

        [Required(ErrorMessage = "الاسم الكامل مطلوب.")]
        [StringLength(100, ErrorMessage = "الاسم الكامل لا يمكن أن يتجاوز 100 حرفًا.")]
        public string FullName
        {
            get;
            set;
        } = string.Empty; // الاسم الكامل

        [Required(ErrorMessage = "دور المستخدم مطلوب.")]
        public UserRole Role
        {
            get;    // دور المستخدم
            set;
        }

        public bool IsActive
        {
            get;
            set;
        } = true; // نشط/غير نشط

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? LastLoginDate
        {
            get;
            set;
        }
        public DateTime? ModifiedDate
        {
            get;
            set;
        }

        // Navigation properties
        public virtual UserPermissions? Permissions
        {
            get;
            set;
        }
    }

    public class UserPermissions
    {
        [Key]
        public int UserPermissionsId
        {
            get;
            set;
        }

        [Required]
        public int UserId
        {
            get;    // ربط بالمستخدم
            set;
        }

        // Inventory permissions - صلاحيات المخزن
        public bool CanAddCar
        {
            get;
            set;
        } = false; // إضافة سيارة للمخزن
        public bool CanEditCar
        {
            get;
            set;
        } = false; // تعديل سيارة في المخزن
        public bool CanDeleteCar
        {
            get;
            set;
        } = false; // حذف سيارة من المخزن
        public bool CanViewInventory
        {
            get;
            set;
        } = false; // عرض المخزن

        // Sales permissions - صلاحيات المبيعات
        public bool CanSell
        {
            get;
            set;
        } = false; // البيع
        public bool CanEditSale
        {
            get;
            set;
        } = false; // تعديل فاتورة بيع
        public bool CanDeleteSale
        {
            get;
            set;
        } = false; // حذف فاتورة بيع
        public bool CanViewSales
        {
            get;
            set;
        } = false; // عرض المبيعات

        // Customer permissions - صلاحيات العملاء
        public bool CanAddCustomer
        {
            get;
            set;
        } = false; // إضافة عميل
        public bool CanEditCustomer
        {
            get;
            set;
        } = false; // تعديل عميل
        public bool CanDeleteCustomer
        {
            get;
            set;
        } = false; // حذف عميل
        public bool CanViewCustomers
        {
            get;
            set;
        } = false; // عرض العملاء
        public bool CanViewCustomerReport
        {
            get;
            set;
        } = false; // عرض تقرير عن عميل معين

        // Supplier permissions - صلاحيات الموردين
        public bool CanManageSuppliers
        {
            get;
            set;
        } = false; // إدارة الموردين

        // Accounting permissions - صلاحيات الحسابات
        public bool CanViewAccounts
        {
            get;
            set;
        } = false; // عرض الحسابات

        // Reports permissions - صلاحيات التقارير
        public bool CanViewGeneralReports
        {
            get;
            set;
        } = false; // عرض تقارير عامة

        // User management permissions - صلاحيات إدارة المستخدمين
        public bool CanManageUsers
        {
            get;
            set;
        } = false; // إدارة المستخدمين (للمدير)
        public bool CanManageSettings
        {
            get;
            set;
        } = false; // إدارة الإعدادات

        // Developer-specific permissions - صلاحيات المطور
        public bool CanAddManager
        {
            get;
            set;
        } = false; // إضافة مدير النشاط
        public bool CanManageManagerPassword
        {
            get;
            set;
        } = false; // إدارة كلمة مرور المدير
        public bool CanActivateSubscription
        {
            get;
            set;
        } = false; // تفعيل الاشتراك
        public bool CanActivateInstallation
        {
            get;
            set;
        } = false; // تفعيل التثبيت
        public bool CanResetSystem
        {
            get;
            set;
        } = false; // إعادة تعيين النظام
        public bool CanRestoreDefaults
        {
            get;
            set;
        } = false; // استعادة الإعدادات الافتراضية

        // Manager-specific permissions - صلاحيات مدير النشاط
        public bool CanFullActivityManagement
        {
            get;
            set;
        } = false; // الإدارة الكاملة للنشاط
        public bool CanCopyDatabase
        {
            get;
            set;
        } = false; // نسخ قاعدة البيانات
        public bool CanArchiveSystem
        {
            get;
            set;
        } = false; // أرشفة النظام
        public bool CanAddSalesRep
        {
            get;
            set;
        } = false; // إضافة مندوب مبيعات
        public bool CanManageSalesRepPassword
        {
            get;
            set;
        } = false; // إدارة كلمة مرور مندوب المبيعات

        // Printing permissions - صلاحيات الطباعة
        public bool CanPrintReports
        {
            get;
            set;
        } = false; // طباعة التقارير
        public bool CanPrintStatements
        {
            get;
            set;
        } = false; // طباعة الكشوفات

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? ModifiedDate
        {
            get;
            set;
        }

        // Navigation properties
        public virtual User User
        {
            get;
            set;
        } = null!;
    }

    public class SystemSettings
    {
        [Key]
        public int SettingsId
        {
            get;
            set;
        }

        // Subscription settings - إعدادات الاشتراك
        public SubscriptionType SubscriptionType
        {
            get;
            set;
        } = SubscriptionType.Yearly; // نوع الاشتراك
        public DateTime? SubscriptionStartDate
        {
            get;    // تاريخ بداية الاشتراك
            set;
        }
        public DateTime? SubscriptionEndDate
        {
            get;    // تاريخ انتهاء الاشتراك
            set;
        }
        public bool IsActive
        {
            get;
            set;
        } = true; // هل الاشتراك نشط

        // Application settings - إعدادات التطبيق
        [StringLength(10, ErrorMessage = "العملة لا يمكن أن تتجاوز 10 أحرف.")]
        public string Currency
        {
            get;
            set;
        } = "USD"; // العملة

        [StringLength(200, ErrorMessage = "اسم الشركة لا يمكن أن يتجاوز 200 حرفًا.")]
        public string CompanyName
        {
            get;
            set;
        } = string.Empty; // اسم الشركة

        [StringLength(500, ErrorMessage = "عنوان الشركة لا يمكن أن يتجاوز 500 حرفًا.")]
        public string CompanyAddress
        {
            get;
            set;
        } = string.Empty; // عنوان الشركة

        [StringLength(20, ErrorMessage = "هاتف الشركة لا يمكن أن يتجاوز 20 حرفًا.")]
        public string CompanyPhone
        {
            get;
            set;
        } = string.Empty; // هاتف الشركة

        [StringLength(500, ErrorMessage = "مسار شعار الشركة لا يمكن أن يتجاوز 500 حرفًا.")]
        public string CompanyLogo
        {
            get;
            set;
        } = string.Empty; // شعار الشركة

        [StringLength(50, ErrorMessage = "رقم السجل التجاري لا يمكن أن يتجاوز 50 حرفًا.")]
        public string CommercialRegister
        {
            get;
            set;
        } = string.Empty; // رقم السجل التجاري

        [StringLength(50, ErrorMessage = "رقم البطاقة الضريبية لا يمكن أن يتجاوز 50 حرفًا.")]
        public string TaxCard
        {
            get;
            set;
        } = string.Empty; // رقم البطاقة الضريبية

        // Gallery settings - إعدادات المعرض
        public bool GalleryDataEnabled
        {
            get;
            set;
        } = false; // تفعيل بيانات المعرض
        public bool ShowCarImages
        {
            get;
            set;
        } = true; // إظهار صور السيارات
        public int MaxImagesPerCar
        {
            get;
            set;
        } = 10; // الحد الأقصى لصور السيارة الواحدة

        // Backup settings - إعدادات النسخ الاحتياطي
        public bool AutoBackupEnabled
        {
            get;
            set;
        } = false; // تفعيل النسخ الاحتياطي التلقائي
        public int BackupIntervalHours
        {
            get;
            set;
        } = 24; // فترة النسخ الاحتياطي بالساعات

        [StringLength(500, ErrorMessage = "مسار النسخ الاحتياطي لا يمكن أن يتجاوز 500 حرفًا.")]
        public string BackupPath
        {
            get;
            set;
        } = string.Empty; // مسار النسخ الاحتياطي

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? ModifiedDate
        {
            get;
            set;
        }
    }

    public enum UserRole
    {
        Developer = 1,          // مبرمج - أعلى صلاحيات
        Manager = 2,            // مدير النشاط
        SalesRepresentative = 3 // مندوب مبيعات
    }

    public enum SubscriptionType
    {
        Monthly = 0,     // شهري
        Quarterly = 1,   // ربع سنوي
        SemiAnnual = 2,  // نصف سنوي
        Yearly = 3       // سنوي
    }
}


