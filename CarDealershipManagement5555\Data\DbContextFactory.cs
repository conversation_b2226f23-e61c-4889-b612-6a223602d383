using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Data
{
public static class DbContextFactory
{
    public static CarDealershipContext CreateContext()
    {
        var connectionString = "Data Source=CarDealership.db;Cache=Shared;Pooling=true;";
        var options = new DbContextOptionsBuilder<CarDealershipContext>()
        .UseSqlite(connectionString, options =>
        {
            options.CommandTimeout(30); // 30 second timeout
        })
        .EnableSensitiveDataLogging() // For better debugging
        .EnableDetailedErrors() // More detailed error information
        .LogTo(Console.WriteLine, Microsoft.Extensions.Logging.LogLevel.Warning) // Log warnings and errors
        .Options;

        return new CarDealershipContext(options);
    }

    public static async Task<bool> TestConnection()
    {
        try
        {
            using var context = CreateContext();
            await context.Database.CanConnectAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public static async Task EnsureDatabaseCreated()
    {
        try
        {
            using var context = CreateContext();
            await context.Database.EnsureCreatedAsync();
        }
        catch(Exception ex)
        {
            throw new InvalidOperationException($"Failed to ensure database is created: {ex.Message}", ex);
        }
    }
}
}
