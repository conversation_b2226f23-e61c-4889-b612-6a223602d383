using CarDealershipManagement.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CarDealershipManagement.Services
{
    /// <summary>
    /// خدمة إدارة الصلاحيات - تحدد الصلاحيات المناسبة لكل دور
    /// </summary>
    public static class PermissionService
    {
        /// <summary>
        /// فئات الصلاحيات المختلفة
        /// </summary>
        public enum PermissionCategory
        {
            Inventory,      // المخزون
            Sales,          // المبيعات
            Customers,      // العملاء
            Suppliers,      // الموردين
            Reports,        // التقارير
            Management,     // الإدارة
            System,         // النظام
            Printing        // الطباعة
        }

        /// <summary>
        /// معلومات الصلاحية
        /// </summary>
        public class PermissionInfo
        {
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public PermissionCategory Category { get; set; }
            public bool DeveloperDefault { get; set; }
            public bool ManagerDefault { get; set; }
            public bool SalesRepDefault { get; set; }
        }

        /// <summary>
        /// قائمة جميع الصلاحيات مع معلوماتها
        /// </summary>
        public static readonly Dictionary<string, PermissionInfo> AllPermissions = new()
        {
            // صلاحيات المخزون - Inventory Permissions
            ["CanViewInventory"] = new()
            {
                Name = "عرض المخزون",
                Description = "عرض قائمة السيارات المتاحة في المخزون",
                Category = PermissionCategory.Inventory,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true
            },
            ["CanAddCar"] = new()
            {
                Name = "إضافة سيارة",
                Description = "إضافة سيارة جديدة للمخزون",
                Category = PermissionCategory.Inventory,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },
            ["CanEditCar"] = new()
            {
                Name = "تعديل سيارة",
                Description = "تعديل بيانات السيارات في المخزون",
                Category = PermissionCategory.Inventory,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },
            ["CanDeleteCar"] = new()
            {
                Name = "حذف سيارة",
                Description = "حذف سيارة من المخزون",
                Category = PermissionCategory.Inventory,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },

            // صلاحيات المبيعات - Sales Permissions
            ["CanSell"] = new()
            {
                Name = "البيع",
                Description = "إنشاء فاتورة بيع جديدة",
                Category = PermissionCategory.Sales,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true
            },
            ["CanViewSales"] = new()
            {
                Name = "عرض المبيعات",
                Description = "عرض قائمة المبيعات",
                Category = PermissionCategory.Sales,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true // يرى مبيعاته فقط
            },
            ["CanEditSale"] = new()
            {
                Name = "تعديل المبيعات",
                Description = "تعديل فواتير البيع",
                Category = PermissionCategory.Sales,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },
            ["CanDeleteSale"] = new()
            {
                Name = "حذف المبيعات",
                Description = "حذف فواتير البيع",
                Category = PermissionCategory.Sales,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },

            // صلاحيات العملاء - Customer Permissions
            ["CanViewCustomers"] = new()
            {
                Name = "عرض العملاء",
                Description = "عرض قائمة العملاء",
                Category = PermissionCategory.Customers,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true
            },
            ["CanAddCustomer"] = new()
            {
                Name = "إضافة عميل",
                Description = "إضافة عميل جديد",
                Category = PermissionCategory.Customers,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true
            },
            ["CanEditCustomer"] = new()
            {
                Name = "تعديل عميل",
                Description = "تعديل بيانات العملاء",
                Category = PermissionCategory.Customers,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true
            },
            ["CanDeleteCustomer"] = new()
            {
                Name = "حذف عميل",
                Description = "حذف عميل من النظام",
                Category = PermissionCategory.Customers,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },
            ["CanViewCustomerReport"] = new()
            {
                Name = "تقرير العميل",
                Description = "عرض تقرير مفصل عن عميل معين",
                Category = PermissionCategory.Customers,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true
            },

            // صلاحيات الموردين - Supplier Permissions
            ["CanManageSuppliers"] = new()
            {
                Name = "إدارة الموردين",
                Description = "إدارة قائمة الموردين",
                Category = PermissionCategory.Suppliers,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },

            // صلاحيات التقارير - Report Permissions
            ["CanViewAccounts"] = new()
            {
                Name = "عرض الحسابات",
                Description = "عرض التقارير المالية والحسابات",
                Category = PermissionCategory.Reports,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },
            ["CanViewGeneralReports"] = new()
            {
                Name = "التقارير العامة",
                Description = "عرض التقارير العامة للنظام",
                Category = PermissionCategory.Reports,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },

            // صلاحيات الإدارة - Management Permissions
            ["CanManageUsers"] = new()
            {
                Name = "إدارة المستخدمين",
                Description = "إضافة وتعديل وحذف المستخدمين",
                Category = PermissionCategory.Management,
                DeveloperDefault = true,
                ManagerDefault = false, // المدير لا يدير المستخدمين
                SalesRepDefault = false
            },
            ["CanManageSettings"] = new()
            {
                Name = "إدارة الإعدادات",
                Description = "تعديل إعدادات النظام",
                Category = PermissionCategory.Management,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },

            // صلاحيات الطباعة - Printing Permissions
            ["CanPrintReports"] = new()
            {
                Name = "طباعة التقارير",
                Description = "طباعة التقارير المختلفة",
                Category = PermissionCategory.Printing,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = false
            },
            ["CanPrintStatements"] = new()
            {
                Name = "طباعة الكشوفات",
                Description = "طباعة كشوفات الحسابات والفواتير",
                Category = PermissionCategory.Printing,
                DeveloperDefault = true,
                ManagerDefault = true,
                SalesRepDefault = true
            }
        };

        /// <summary>
        /// الحصول على الصلاحيات الافتراضية لدور معين
        /// </summary>
        public static UserPermissions GetDefaultPermissionsForRole(UserRole role)
        {
            var permissions = new UserPermissions();

            foreach (var permission in AllPermissions)
            {
                var permissionName = permission.Key;
                var permissionInfo = permission.Value;
                
                bool shouldEnable = role switch
                {
                    UserRole.Developer => permissionInfo.DeveloperDefault,
                    UserRole.Manager => permissionInfo.ManagerDefault,
                    UserRole.SalesRepresentative => permissionInfo.SalesRepDefault,
                    _ => false
                };

                // استخدام Reflection لتعيين القيمة
                var property = typeof(UserPermissions).GetProperty(permissionName);
                property?.SetValue(permissions, shouldEnable);
            }

            // إضافة الصلاحيات الخاصة بكل دور
            SetRoleSpecificPermissions(permissions, role);

            return permissions;
        }

        /// <summary>
        /// تعيين الصلاحيات الخاصة بكل دور
        /// </summary>
        private static void SetRoleSpecificPermissions(UserPermissions permissions, UserRole role)
        {
            switch (role)
            {
                case UserRole.Developer:
                    // المطور له جميع الصلاحيات الخاصة
                    permissions.CanAddManager = true;
                    permissions.CanManageManagerPassword = true;
                    permissions.CanActivateSubscription = true;
                    permissions.CanActivateInstallation = true;
                    permissions.CanResetSystem = true;
                    permissions.CanRestoreDefaults = true;
                    break;

                case UserRole.Manager:
                    // المدير له صلاحيات إدارة النشاط
                    permissions.CanFullActivityManagement = true;
                    permissions.CanCopyDatabase = true;
                    permissions.CanArchiveSystem = true;
                    permissions.CanAddSalesRep = true;
                    permissions.CanManageSalesRepPassword = true;
                    break;

                case UserRole.SalesRepresentative:
                    // المندوب ليس له صلاحيات خاصة إضافية
                    break;
            }
        }

        /// <summary>
        /// الحصول على الصلاحيات مجمعة حسب الفئة
        /// </summary>
        public static Dictionary<PermissionCategory, List<KeyValuePair<string, PermissionInfo>>> GetPermissionsByCategory()
        {
            return AllPermissions
                .GroupBy(p => p.Value.Category)
                .ToDictionary(
                    g => g.Key,
                    g => g.ToList()
                );
        }

        /// <summary>
        /// التحقق من صلاحية معينة للمستخدم
        /// </summary>
        public static bool HasPermission(UserPermissions? permissions, string permissionName)
        {
            if (permissions == null) return false;

            var property = typeof(UserPermissions).GetProperty(permissionName);
            return property?.GetValue(permissions) as bool? ?? false;
        }

        /// <summary>
        /// الحصول على اسم الفئة بالعربية
        /// </summary>
        public static string GetCategoryDisplayName(PermissionCategory category)
        {
            return category switch
            {
                PermissionCategory.Inventory => "المخزون",
                PermissionCategory.Sales => "المبيعات",
                PermissionCategory.Customers => "العملاء",
                PermissionCategory.Suppliers => "الموردين",
                PermissionCategory.Reports => "التقارير",
                PermissionCategory.Management => "الإدارة",
                PermissionCategory.System => "النظام",
                PermissionCategory.Printing => "الطباعة",
                _ => "غير محدد"
            };
        }
    }
}
