# Car Dealership Management System - Daily Backup Script
# Version: 1.0.0
# Last Updated: July 4, 2025

param(
    [string]$BackupPath = "TestBackup",
    [int]$RetentionDays = 30,
    [switch]$Verbose
)

function Write-Log {
    param($Message, $Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    
    if ($Verbose) {
        Write-Host $LogMessage -ForegroundColor $(
            switch($Level) {
                "ERROR" { "Red" }
                "WARN" { "Yellow" }
                "INFO" { "Green" }
                default { "White" }
            }
        )
    }
    
    # Log to file
    $LogFile = Join-Path $PSScriptRoot "Logs\backup.log"
    if (!(Test-Path (Split-Path $LogFile))) {
        New-Item -ItemType Directory -Path (Split-Path $LogFile) -Force | Out-Null
    }
    Add-Content -Path $LogFile -Value $LogMessage
}

function Test-DatabaseLock {
    # Check if database is being used
    $DbPath = Join-Path $PSScriptRoot "..\CarDealership.db"
    try {
        $fileStream = [System.IO.File]::Open($DbPath, "Open", "Read", "None")
        $fileStream.Close()
        return $false
    } catch {
        return $true
    }
}

function New-Backup {
    try {
        Write-Log "Starting backup process..."
        
        # Check if backup directory exists
        if (!(Test-Path $BackupPath)) {
            New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
            Write-Log "Created backup directory: $BackupPath"
        }
        
        $Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $BackupFolder = Join-Path $BackupPath $Timestamp
        
        # Create timestamped backup folder
        New-Item -ItemType Directory -Path $BackupFolder -Force | Out-Null
        
        # Check if database is locked
        if (Test-DatabaseLock) {
            Write-Log "Database is currently in use. Attempting to create backup anyway..." "WARN"
        }
        
        # Backup database file
        $DbPath = Join-Path $PSScriptRoot "..\CarDealership.db"
        if (Test-Path $DbPath) {
            $DbBackupPath = Join-Path $BackupFolder "CarDealership_$Timestamp.db"
            Copy-Item $DbPath -Destination $DbBackupPath -Force
            
            $DbSize = (Get-Item $DbBackupPath).Length / 1MB
            Write-Log "Database backed up successfully (Size: $([math]::Round($DbSize, 2)) MB)"
        } else {
            Write-Log "Database file not found at: $DbPath" "ERROR"
            return $false
        }
        
        # Backup application logs
        if (Test-Path "Logs") {
            $LogsBackupPath = Join-Path $BackupFolder "Logs"
            Copy-Item "Logs" -Destination $LogsBackupPath -Recurse -Force
            $LogFiles = (Get-ChildItem $LogsBackupPath -Recurse -File).Count
            Write-Log "Application logs backed up ($LogFiles files)"
        }
        
        # Backup user documents (if any)
        $DocumentPaths = @("Documents", "Files", "Attachments")
        foreach ($DocPath in $DocumentPaths) {
            if (Test-Path $DocPath) {
                $DocBackupPath = Join-Path $BackupFolder $DocPath
                Copy-Item $DocPath -Destination $DocBackupPath -Recurse -Force
                $DocFiles = (Get-ChildItem $DocBackupPath -Recurse -File).Count
                Write-Log "User documents backed up from $DocPath ($DocFiles files)"
            }
        }
        
        # Backup configuration files
        $ConfigFiles = @("appsettings.json", "*.config", "settings.json")
        foreach ($ConfigPattern in $ConfigFiles) {
            $Files = Get-ChildItem -Filter $ConfigPattern -ErrorAction SilentlyContinue
            foreach ($File in $Files) {
                Copy-Item $File.FullName -Destination $BackupFolder -Force
                Write-Log "Configuration file backed up: $($File.Name)"
            }
        }
        
        # Create backup manifest
        $ManifestPath = Join-Path $BackupFolder "backup_manifest.json"
        $Manifest = @{
            BackupDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            BackupVersion = "1.0.0"
            DatabaseSize = if (Test-Path $DbBackupPath) { (Get-Item $DbBackupPath).Length } else { 0 }
            Files = Get-ChildItem $BackupFolder -Recurse -File | ForEach-Object {
                @{
                    Name = $_.Name
                    Size = $_.Length
                    Path = $_.FullName.Replace($BackupFolder, "")
                    LastModified = $_.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
                }
            }
            SystemInfo = @{
                ComputerName = $env:COMPUTERNAME
                UserName = $env:USERNAME
                OSVersion = [System.Environment]::OSVersion.VersionString
                DotNETVersion = [System.Environment]::Version.ToString()
            }
        }
        $Manifest | ConvertTo-Json -Depth 4 | Out-File $ManifestPath -Encoding UTF8
        Write-Log "Backup manifest created"
        
        # Compress backup
        $CompressedBackup = "$BackupFolder.zip"
        Compress-Archive -Path "$BackupFolder\*" -DestinationPath $CompressedBackup -Force
        
        # Verify compressed backup
        if (Test-Path $CompressedBackup) {
            $CompressedSize = (Get-Item $CompressedBackup).Length / 1MB
            Write-Log "Backup compressed successfully (Size: $([math]::Round($CompressedSize, 2)) MB)"
            
            # Remove uncompressed folder
            Remove-Item $BackupFolder -Recurse -Force
            Write-Log "Temporary backup folder cleaned up"
            
            return $CompressedBackup
        } else {
            Write-Log "Failed to create compressed backup!" "ERROR"
            return $false
        }
        
    } catch {
        Write-Log "Error during backup: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Remove-OldBackups {
    param([int]$RetentionDays)
    
    try {
        $CutoffDate = (Get-Date).AddDays(-$RetentionDays)
        $OldBackups = Get-ChildItem -Path $BackupPath -Filter "*.zip" | 
                      Where-Object { $_.CreationTime -lt $CutoffDate }
        
        foreach ($OldBackup in $OldBackups) {
            Remove-Item $OldBackup.FullName -Force
            Write-Log "Removed old backup: $($OldBackup.Name)"
        }
        
        if ($OldBackups.Count -eq 0) {
            Write-Log "No old backups to remove"
        } else {
            Write-Log "Removed $($OldBackups.Count) old backup(s)"
        }
        
    } catch {
        Write-Log "Error removing old backups: $($_.Exception.Message)" "ERROR"
    }
}

function Test-BackupIntegrity {
    param([string]$BackupFile)
    
    try {
        # Test if zip file can be opened
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $archive = [System.IO.Compression.ZipFile]::OpenRead($BackupFile)
        
        # Check for required files
        $requiredFiles = @("*.db", "backup_manifest.json")
        $foundFiles = @()
        
        foreach ($entry in $archive.Entries) {
            foreach ($pattern in $requiredFiles) {
                if ($entry.Name -like $pattern) {
                    $foundFiles += $pattern
                }
            }
        }
        
        $archive.Dispose()
        
        $isValid = $foundFiles.Count -eq $requiredFiles.Count
        if ($isValid) {
            Write-Log "Backup integrity verified successfully"
        } else {
            Write-Log "Backup integrity check failed - missing required files" "ERROR"
        }
        
        return $isValid
        
    } catch {
        Write-Log "Error verifying backup integrity: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Send-BackupNotification {
    param([string]$BackupFile, [bool]$Success)
    
    # This function can be extended to send email notifications
    # For now, it creates a simple status file
    
    $StatusFile = Join-Path $BackupPath "last_backup_status.json"
    $Status = @{
        LastBackupDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Success = $Success
        BackupFile = if ($Success) { Split-Path $BackupFile -Leaf } else { "N/A" }
        Message = if ($Success) { "Backup completed successfully" } else { "Backup failed" }
    }
    
    $Status | ConvertTo-Json | Out-File $StatusFile -Encoding UTF8
    
    Write-Log "Backup status updated: $($Status.Message)"
}

# Main execution
Write-Log "========================================"
Write-Log "Car Dealership Management System Backup"
Write-Log "========================================"
Write-Log "Backup started at $(Get-Date)"
Write-Log "Backup path: $BackupPath"
Write-Log "Retention days: $RetentionDays"

# Create backup
$BackupResult = New-Backup

if ($BackupResult) {
    Write-Log "Backup created: $BackupResult"
    
    # Verify backup integrity
    $IntegrityCheck = Test-BackupIntegrity -BackupFile $BackupResult
    
    if ($IntegrityCheck) {
        # Clean old backups
        Remove-OldBackups -RetentionDays $RetentionDays
        
        # Send success notification
        Send-BackupNotification -BackupFile $BackupResult -Success $true
        
        Write-Log "Backup process completed successfully"
        Write-Log "========================================"
        
        exit 0
    } else {
        Write-Log "Backup verification failed!" "ERROR"
        Send-BackupNotification -BackupFile $BackupResult -Success $false
        exit 1
    }
} else {
    Write-Log "Backup process failed!" "ERROR"
    Send-BackupNotification -BackupFile "" -Success $false
    Write-Log "========================================"
    exit 1
}
