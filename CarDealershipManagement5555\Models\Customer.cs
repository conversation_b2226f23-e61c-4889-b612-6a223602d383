using System.ComponentModel.DataAnnotations;

namespace CarDealershipManagement.Models
{
    public class Customer
    {
        [Key]
        public int CustomerId
        {
            get;
            set;
        }

        [Required(ErrorMessage = "الاسم بالكامل مطلوب.")]
        [StringLength(100, ErrorMessage = "الاسم بالكامل لا يمكن أن يتجاوز 100 حرف.")]
        public string FullName
        {
            get;
            set;
        } = string.Empty; // الاسم بالكامل

        [Required(ErrorMessage = "رقم الهوية/البطاقة الشخصية مطلوب.")]
        [StringLength(50, ErrorMessage = "رقم الهوية/البطاقة الشخصية لا يمكن أن يتجاوز 50 حرفًا.")]
        public string IdNumber
        {
            get;
            set;
        } = string.Empty; // رقم الهوية/البطاقة الشخصية

        [Required(ErrorMessage = "تاريخ الميلاد مطلوب.")]
        public DateTime DateOfBirth
        {
            get;    // تاريخ الميلاد
            set;
        }

        [Required(ErrorMessage = "الدولة مطلوبة.")]
        [StringLength(50, ErrorMessage = "الدولة لا يمكن أن تتجاوز 50 حرفًا.")]
        public string Country
        {
            get;
            set;
        } = string.Empty; // الدولة

        [Required(ErrorMessage = "المدينة مطلوبة.")]
        [StringLength(50, ErrorMessage = "المدينة لا يمكن أن تتجاوز 50 حرفًا.")]
        public string City
        {
            get;
            set;
        } = string.Empty; // المدينة

        [Required(ErrorMessage = "المنطقة مطلوبة.")]
        [StringLength(50, ErrorMessage = "المنطقة لا يمكن أن تتجاوز 50 حرفًا.")]
        public string Area
        {
            get;
            set;
        } = string.Empty; // المنطقة

        [Required(ErrorMessage = "الشارع مطلوب.")]
        [StringLength(200, ErrorMessage = "الشارع لا يمكن أن يتجاوز 200 حرفًا.")]
        public string Street
        {
            get;
            set;
        } = string.Empty; // الشارع

        [Required(ErrorMessage = "رقم الهاتف الأساسي مطلوب.")]
        [StringLength(20, ErrorMessage = "رقم الهاتف الأساسي لا يمكن أن يتجاوز 20 حرفًا.")]
        [Phone(ErrorMessage = "صيغة رقم الهاتف الأساسي غير صحيحة.")]
        public string PrimaryPhone
        {
            get;
            set;
        } = string.Empty; // رقم الهاتف الأساسي

        [StringLength(20, ErrorMessage = "رقم الهاتف الثانوي لا يمكن أن يتجاوز 20 حرفًا.")]
        [Phone(ErrorMessage = "صيغة رقم الهاتف الثانوي غير صحيحة.")]
        public string? SecondaryPhone
        {
            get;    // رقم هاتف ثانوي (اختياري)
            set;
        }

        [StringLength(100, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرفًا.")]
        [EmailAddress(ErrorMessage = "صيغة البريد الإلكتروني غير صحيحة.")]
        public string? Email
        {
            get;    // البريد الإلكتروني (اختياري)
            set;
        }

        // Status and soft delete fields
        public bool IsActive
        {
            get;
            set;
        } = true; // العميل نشط
        public bool IsDeleted
        {
            get;
            set;
        } = false; // حذف ناعم
        public DateTime? DeletedDate
        {
            get;    // تاريخ الحذف
            set;
        }
        public CustomerStatus Status
        {
            get;
            set;
        } = CustomerStatus.Active; // حالة العميل

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? ModifiedDate
        {
            get;
            set;
        }

        // Navigation properties
        public virtual ICollection<CustomerDocument> Documents
        {
            get;
            set;
        } = new List<CustomerDocument>();
        public virtual ICollection<Sale> Sales
        {
            get;
            set;
        } = new List<Sale>();
    }

    public enum CustomerStatus
    {
        Active = 1,        // نشط
        Inactive = 2,      // غير نشط
        Archived = 3,      // مؤرشف
        Blacklisted = 4    // القائمة السوداء
    }
}


