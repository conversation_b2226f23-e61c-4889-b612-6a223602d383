# Deployment Guide
## Car Dealership Management System

**Version**: 1.0.0  
**Last Updated**: July 4, 2025

## Overview

This guide covers deployment, backup, and maintenance procedures for the Car Dealership Management System.

## System Requirements

### Target Environment
- **Operating System**: Windows 10 (version 1903+) or Windows 11
- **Processor**: x64 architecture
- **.NET Runtime**: .NET 8.0 Desktop Runtime
- **Memory**: Minimum 2GB RAM, Recommended 4GB
- **Storage**: 1GB available space for application and data
- **Display**: 1920x1080 minimum resolution

### Dependencies
- .NET 8.0 Desktop Runtime (Windows x64)
- Visual C++ Redistributable (latest)
- Windows Defender or compatible antivirus (whitelist application)

## Deployment Methods

### Method 1: Standalone Deployment (Recommended)

#### Step 1: Build Self-Contained Application
```bash
# Navigate to project directory
cd CarDealershipManagement

# Clean previous builds
dotnet clean

# Publish self-contained application
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true
```

#### Step 2: Create Installation Package
```powershell
# Create deployment folder
New-Item -ItemType Directory -Path "Deploy" -Force

# Copy published files
Copy-Item "bin\Release\net8.0-windows\win-x64\publish\*" -Destination "Deploy\" -Recurse

# Copy additional files
Copy-Item "README.md" -Destination "Deploy\"
Copy-Item "DEPLOYMENT_GUIDE.md" -Destination "Deploy\"
Copy-Item "LICENSE" -Destination "Deploy\" -ErrorAction SilentlyContinue
```

#### Step 3: Create Installation Script
Create `Deploy\install.bat`:
```batch
@echo off
echo Installing Car Dealership Management System...

:: Check if .NET 8 Desktop Runtime is installed
reg query "HKLM\SOFTWARE\dotnet\Setup\InstalledVersions\x64\Microsoft.WindowsDesktop.App" /v "8.0" >nul 2>&1
if errorlevel 1 (
    echo .NET 8.0 Desktop Runtime not found!
    echo Please install .NET 8.0 Desktop Runtime first.
    echo Download from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

:: Create application directory
if not exist "C:\Program Files\CarDealershipManagement" (
    mkdir "C:\Program Files\CarDealershipManagement"
)

:: Copy application files
xcopy /E /Y /I "%~dp0*" "C:\Program Files\CarDealershipManagement\"

:: Create desktop shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('$([Environment]::GetFolderPath('Desktop'))\Car Dealership Management.lnk'); $Shortcut.TargetPath = 'C:\Program Files\CarDealershipManagement\CarDealershipManagement.exe'; $Shortcut.IconLocation = 'C:\Program Files\CarDealershipManagement\CarDealershipManagement.exe'; $Shortcut.Save()"

echo Installation completed successfully!
echo You can now run the application from the desktop shortcut.
pause
```

### Method 2: Framework-Dependent Deployment

#### For environments with .NET 8 already installed:
```bash
dotnet publish -c Release -r win-x64 --self-contained false
```

## Database Deployment

### Initial Database Setup
The application automatically creates the SQLite database on first run:
- Database file: `CarDealership.db`
- Location: Application directory
- Initial user: `amrali` / `braa`

### Database Migration (Future Versions)
```bash
# Add new migration
dotnet ef migrations add MigrationName

# Update database
dotnet ef database update
```

## Configuration Management

### Application Settings
Configuration stored in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=CarDealership.db"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  }
}
```

### Environment-Specific Configuration
- Development: `appsettings.Development.json`
- Production: `appsettings.Production.json`

## Backup Strategy

### Automated Backup System

#### Daily Backup Script (`backup_daily.ps1`)
```powershell
param(
    [string]$BackupPath = "C:\Backups\CarDealership",
    [int]$RetentionDays = 30
)

$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupFolder = Join-Path $BackupPath $Timestamp

# Create backup directory
New-Item -ItemType Directory -Path $BackupFolder -Force

# Backup database
Copy-Item "CarDealership.db" -Destination "$BackupFolder\CarDealership_$Timestamp.db"

# Backup application logs
if (Test-Path "Logs") {
    Copy-Item "Logs\*" -Destination "$BackupFolder\Logs\" -Recurse -Force
}

# Backup user documents (if any)
if (Test-Path "Documents") {
    Copy-Item "Documents\*" -Destination "$BackupFolder\Documents\" -Recurse -Force
}

# Compress backup
Compress-Archive -Path "$BackupFolder\*" -DestinationPath "$BackupFolder.zip"
Remove-Item $BackupFolder -Recurse -Force

# Clean old backups
Get-ChildItem -Path $BackupPath -Filter "*.zip" | 
    Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-$RetentionDays) } |
    Remove-Item -Force

Write-Host "Backup completed: $BackupFolder.zip"
```

#### Schedule Automated Backups
```powershell
# Create scheduled task for daily backups
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Program Files\CarDealershipManagement\backup_daily.ps1"
$Trigger = New-ScheduledTaskTrigger -Daily -At "02:00"
$Settings = New-ScheduledTaskSettingsSet -ExecutionTimeLimit (New-TimeSpan -Hours 1)
$Principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount

Register-ScheduledTask -TaskName "CarDealership_DailyBackup" -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal
```

### Manual Backup Procedures

#### Database Backup
```powershell
# Quick database backup
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
Copy-Item "CarDealership.db" -Destination "Backup_CarDealership_$Timestamp.db"
```

#### Full System Backup
```powershell
# Complete application backup
$BackupName = "CarDealership_FullBackup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Compress-Archive -Path "C:\Program Files\CarDealershipManagement\*" -DestinationPath "C:\Backups\$BackupName.zip"
```

## Recovery Procedures

### Database Recovery
1. Stop the application
2. Replace `CarDealership.db` with backup file
3. Restart the application
4. Verify data integrity

### Full System Recovery
1. Uninstall current application
2. Install fresh copy
3. Replace database with backup
4. Restore user documents/settings

### Emergency Recovery
```powershell
# Emergency recovery script
param([string]$BackupFile)

# Stop application if running
Get-Process "CarDealershipManagement" -ErrorAction SilentlyContinue | Stop-Process -Force

# Extract backup
Expand-Archive -Path $BackupFile -DestinationPath "Recovery_Temp" -Force

# Restore database
Copy-Item "Recovery_Temp\*.db" -Destination "CarDealership.db" -Force

# Clean up
Remove-Item "Recovery_Temp" -Recurse -Force

Write-Host "Recovery completed. Please restart the application."
```

## Monitoring and Maintenance

### Health Checks

#### System Health Script (`health_check.ps1`)
```powershell
# Check application status
$AppRunning = Get-Process "CarDealershipManagement" -ErrorAction SilentlyContinue
Write-Host "Application Running: $($AppRunning -ne $null)"

# Check database file
$DbExists = Test-Path "CarDealership.db"
$DbSize = if ($DbExists) { (Get-Item "CarDealership.db").Length } else { 0 }
Write-Host "Database File: $DbExists ($([math]::Round($DbSize/1MB, 2)) MB)"

# Check disk space
$FreeSpace = (Get-WmiObject -Class Win32_LogicalDisk | Where-Object DeviceID -eq "C:").FreeSpace / 1GB
Write-Host "Free Disk Space: $([math]::Round($FreeSpace, 2)) GB"

# Check last backup
$LastBackup = Get-ChildItem "C:\Backups\CarDealership\*.zip" -ErrorAction SilentlyContinue | 
               Sort-Object CreationTime -Descending | 
               Select-Object -First 1
Write-Host "Last Backup: $($LastBackup.CreationTime)"
```

### Log Management
```powershell
# Log rotation script
$LogPath = "Logs"
$MaxLogAge = 90  # days

Get-ChildItem -Path $LogPath -Filter "*.log" |
    Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-$MaxLogAge) } |
    Remove-Item -Force
```

## Security Considerations

### File System Permissions
```powershell
# Set appropriate permissions
icacls "C:\Program Files\CarDealershipManagement" /grant "Users:(RX)" /T
icacls "C:\Program Files\CarDealershipManagement\CarDealership.db" /grant "Users:(M)"
```

### Antivirus Exclusions
Add these paths to antivirus exclusions:
- `C:\Program Files\CarDealershipManagement\`
- `C:\Backups\CarDealership\`

### Network Security
- Block unnecessary network access
- Enable Windows Firewall
- Use encrypted backup storage (if network backups)

## Troubleshooting

### Common Issues

#### Application Won't Start
1. Check .NET 8 Desktop Runtime installation
2. Verify database file permissions
3. Check antivirus exclusions
4. Run as administrator

#### Database Corruption
1. Stop application
2. Restore from latest backup
3. Check disk for errors: `chkdsk C: /f`
4. Restart application

#### Performance Issues
1. Check available RAM and disk space
2. Close unnecessary applications
3. Defragment hard drive
4. Check for Windows updates

### Log Analysis
```powershell
# Analyze error logs
Get-Content "Logs\*.log" | Where-Object { $_ -match "ERROR|FATAL" } | Select-Object -Last 50
```

## Update Procedures

### Application Updates
1. Backup current system
2. Download new version
3. Stop application
4. Replace application files
5. Run database migrations (if needed)
6. Test functionality
7. Update documentation

### Rollback Procedure
1. Stop new application
2. Restore previous version from backup
3. Restore database from backup
4. Restart application
5. Verify functionality

## Support and Contacts

### System Administrator
- **Primary**: [Admin Name] - [Email] - [Phone]
- **Secondary**: [Backup Admin] - [Email] - [Phone]

### Vendor Support
- **Technical Support**: [Support Email]
- **Emergency Contact**: [Emergency Phone]

### Documentation
- User Manual: `README.md`
- Security Guide: `SECURITY_AUDIT.md`
- This Deployment Guide: `DEPLOYMENT_GUIDE.md`

---

**Document Version**: 1.0  
**Last Review**: July 4, 2025  
**Next Review**: October 4, 2025
