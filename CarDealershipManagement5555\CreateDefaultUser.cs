using System;
using System.IO;
using Microsoft.Data.Sqlite;
using BCrypt.Net;

namespace CarDealershipManagement
{
    public class CreateDefaultUser
    {
        public static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("===============================================");
                Console.WriteLine("        CREATE DEFAULT USER TOOL");
                Console.WriteLine("        Car Dealership Management System");
                Console.WriteLine("===============================================");
                Console.WriteLine();

                string dbPath = "CarDealership.db";
                string connectionString = $"Data Source={dbPath}";

                Console.WriteLine("Step 1: Creating database and tables...");
                
                using (var connection = new SqliteConnection(connectionString))
                {
                    connection.Open();

                    // Create Users table
                    string createUsersTable = @"
                        CREATE TABLE IF NOT EXISTS Users (
                            UserId INTEGER PRIMARY KEY AUTOINCREMENT,
                            Username TEXT NOT NULL UNIQUE,
                            PasswordHash TEXT NOT NULL,
                            FullName TEXT NOT NULL,
                            Role INTEGER NOT NULL,
                            IsActive INTEGER NOT NULL DEFAULT 1,
                            CreatedDate TEXT NOT NULL,
                            LastLoginDate TEXT,
                            ModifiedDate TEXT
                        )";

                    using (var command = new SqliteCommand(createUsersTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    Console.WriteLine("✅ Users table created");

                    // Create UserPermissions table
                    string createPermissionsTable = @"
                        CREATE TABLE IF NOT EXISTS UserPermissions (
                            PermissionId INTEGER PRIMARY KEY AUTOINCREMENT,
                            UserId INTEGER NOT NULL,
                            CanAddCar INTEGER NOT NULL DEFAULT 0,
                            CanEditCar INTEGER NOT NULL DEFAULT 0,
                            CanDeleteCar INTEGER NOT NULL DEFAULT 0,
                            CanViewInventory INTEGER NOT NULL DEFAULT 0,
                            CanSell INTEGER NOT NULL DEFAULT 0,
                            CanEditSale INTEGER NOT NULL DEFAULT 0,
                            CanDeleteSale INTEGER NOT NULL DEFAULT 0,
                            CanViewSales INTEGER NOT NULL DEFAULT 0,
                            CanAddCustomer INTEGER NOT NULL DEFAULT 0,
                            CanEditCustomer INTEGER NOT NULL DEFAULT 0,
                            CanDeleteCustomer INTEGER NOT NULL DEFAULT 0,
                            CanViewCustomers INTEGER NOT NULL DEFAULT 0,
                            CanViewCustomerReport INTEGER NOT NULL DEFAULT 0,
                            CanManageSuppliers INTEGER NOT NULL DEFAULT 0,
                            CanViewAccounts INTEGER NOT NULL DEFAULT 0,
                            CanViewGeneralReports INTEGER NOT NULL DEFAULT 0,
                            CanManageUsers INTEGER NOT NULL DEFAULT 0,
                            CanManageSettings INTEGER NOT NULL DEFAULT 0,
                            CanPrintReports INTEGER NOT NULL DEFAULT 0,
                            CanPrintStatements INTEGER NOT NULL DEFAULT 0,
                            FOREIGN KEY (UserId) REFERENCES Users (UserId)
                        )";

                    using (var command = new SqliteCommand(createPermissionsTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    Console.WriteLine("✅ UserPermissions table created");

                    // Check if default user already exists
                    string checkUserQuery = "SELECT COUNT(*) FROM Users WHERE Username = 'amrali'";
                    using (var command = new SqliteCommand(checkUserQuery, connection))
                    {
                        int userCount = Convert.ToInt32(command.ExecuteScalar());
                        if (userCount > 0)
                        {
                            Console.WriteLine("⚠️  Default user already exists. Updating password...");
                            
                            // Update password
                            string hashedPassword = BCrypt.HashPassword("braa");
                            string updateQuery = "UPDATE Users SET PasswordHash = @password WHERE Username = 'amrali'";
                            using (var updateCommand = new SqliteCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@password", hashedPassword);
                                updateCommand.ExecuteNonQuery();
                            }
                            Console.WriteLine("✅ Password updated successfully");
                        }
                        else
                        {
                            Console.WriteLine("Step 2: Creating default user...");
                            
                            // Create default user
                            string hashedPassword = BCrypt.HashPassword("braa");
                            string insertUserQuery = @"
                                INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive, CreatedDate)
                                VALUES (@username, @password, @fullname, @role, @active, @created)";

                            using (var command = new SqliteCommand(insertUserQuery, connection))
                            {
                                command.Parameters.AddWithValue("@username", "amrali");
                                command.Parameters.AddWithValue("@password", hashedPassword);
                                command.Parameters.AddWithValue("@fullname", "عمرو علي");
                                command.Parameters.AddWithValue("@role", 1); // Developer
                                command.Parameters.AddWithValue("@active", 1);
                                command.Parameters.AddWithValue("@created", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                                command.ExecuteNonQuery();
                            }

                            Console.WriteLine("✅ Default user created");

                            // Get the user ID
                            string getUserIdQuery = "SELECT UserId FROM Users WHERE Username = 'amrali'";
                            int userId;
                            using (var command = new SqliteCommand(getUserIdQuery, connection))
                            {
                                userId = Convert.ToInt32(command.ExecuteScalar());
                            }

                            // Create permissions for default user (all permissions enabled)
                            string insertPermissionsQuery = @"
                                INSERT INTO UserPermissions (
                                    UserId, CanAddCar, CanEditCar, CanDeleteCar, CanViewInventory,
                                    CanSell, CanEditSale, CanDeleteSale, CanViewSales,
                                    CanAddCustomer, CanEditCustomer, CanDeleteCustomer, CanViewCustomers, CanViewCustomerReport,
                                    CanManageSuppliers, CanViewAccounts, CanViewGeneralReports,
                                    CanManageUsers, CanManageSettings, CanPrintReports, CanPrintStatements
                                ) VALUES (
                                    @userId, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
                                )";

                            using (var command = new SqliteCommand(insertPermissionsQuery, connection))
                            {
                                command.Parameters.AddWithValue("@userId", userId);
                                command.ExecuteNonQuery();
                            }

                            Console.WriteLine("✅ Default permissions created");
                        }
                    }
                }

                Console.WriteLine();
                Console.WriteLine("===============================================");
                Console.WriteLine("           SUCCESS!");
                Console.WriteLine("===============================================");
                Console.WriteLine();
                Console.WriteLine("✅ Database and default user created successfully");
                Console.WriteLine();
                Console.WriteLine("Login credentials:");
                Console.WriteLine("Username: amrali");
                Console.WriteLine("Password: braa");
                Console.WriteLine();
                Console.WriteLine("You can now login to the application!");
                Console.WriteLine("===============================================");
            }
            catch (Exception ex)
            {
                Console.WriteLine();
                Console.WriteLine("❌ Error occurred:");
                Console.WriteLine(ex.Message);
                Console.WriteLine();
                Console.WriteLine("Please try running 'fix_login.bat' instead.");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
