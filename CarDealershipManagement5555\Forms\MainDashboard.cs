using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using CarDealershipManagement.Data;
using CarDealershipManagement.Helpers;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class MainDashboard : Form
    {
        private User? currentUser;
        private MenuStrip menuStrip = null!;
        private StatusStrip statusStrip = null!;
        private ToolStripStatusLabel statusLabel = null!;
        private ToolStripStatusLabel userLabel = null!;
        private Panel mainPanel = null!;
        private PictureBox logoBox = null!;
        private Label companyNameLabel = null!;
        private InstallationService installationService = null!;
        private CarDealershipContext context = null!;
        private SystemSettings? systemSettings = null!;

        public MainDashboard(User user)
        {
            currentUser = user;
            var options = new DbContextOptionsBuilder<CarDealershipContext>()
                .UseSqlite("Data Source=CarDealership.db")
                .Options;

            context = new CarDealershipContext(options);
            installationService = new InstallationService(context);

            InitializeComponent();

            // تحديث قاعدة البيانات أولاً
            UpdateDatabaseAsync();

            // عرض معلومات الترخيص في شريط الحالة
            try
            {
                ShowLicenseInfo();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, false, "خطأ في عرض معلومات الترخيص");
            }

            LoadCompanySettings();
            SetupMenuBasedOnPermissions();
        }

        private void InitializeComponent()
        {
            // Form properties - تحسين للشاشة الكاملة
            this.Text = "🚗 Car Dealership Management System - نظام إدارة معرض السيارات";
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Segoe UI", 10F);
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1400, 900);

            // تطبيق إعدادات النافذة المرنة للشاشة الكاملة
            ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this,
                Screen.PrimaryScreen.WorkingArea.Width,
                Screen.PrimaryScreen.WorkingArea.Height);

            // Create menu strip with improved styling and larger size for full screen
            menuStrip = new MenuStrip()
            {
                BackColor = Color.FromArgb(52, 58, 64),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                Padding = new Padding(20, 15, 0, 15),
                Height = 70
            };

            // Style menu items
            menuStrip.Renderer = new ToolStripProfessionalRenderer(new CustomColorTable());

            // Create status strip with improved styling for full screen
            statusStrip = new StatusStrip()
            {
                BackColor = Color.FromArgb(248, 249, 250),
                ForeColor = Color.FromArgb(52, 58, 64),
                Height = 35
            };
            statusLabel = new ToolStripStatusLabel("✅ جاهز")
            {
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(40, 167, 69)
            };
            // Handle user label based on whether user is logged in
            userLabel = new ToolStripStatusLabel("🔓 وضع بدون مصادقة | جميع الأذونات متاحة")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(108, 117, 125)
            };

            if (currentUser != null)
            {
                userLabel.Text = $"👤 المستخدم: {currentUser.FullName} | 🔑 الدور: {GetRoleDisplayName(currentUser.Role)}";
                userLabel.ForeColor = Color.FromArgb(0, 123, 255);
            }

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, userLabel });

            // Create main panel with gradient background
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Add logo
            logoBox = new PictureBox
            {
                Size = new Size(120, 80),
                Location = new Point(50, 50),
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.Transparent
            };

            // Company name label - will be updated with actual company name from database
            companyNameLabel = new Label
            {
                Text = "معرض السيارات", // Default text, will be updated
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                Location = new Point(190, 50),
                Size = new Size(400, 30),
                ForeColor = Color.FromArgb(52, 58, 64),
                BackColor = Color.Transparent
            };

            // Add welcome label with improved styling
            var welcomeLabel = new Label
            {
                Text = currentUser != null ? $"مرحباً {currentUser.FullName}\nأهلاً بك في نظام إدارة معرض السيارات" : "مرحباً بك في نظام إدارة معرض السيارات\nوضع بدون مصادقة - جميع الأذونات متاحة",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(50, 200),
                Size = new Size(this.Width - 100, 100),
                ForeColor = Color.FromArgb(52, 58, 64),
                BackColor = Color.Transparent
            };

            // إنشاء لوحة الأزرار الرئيسية في صف واحد
            var buttonPanel = new Panel
            {
                Size = new Size(1000, 100),
                Location = new Point((this.Width - 1000) / 2, 350),
                BackColor = Color.Transparent
            };

            // زر الأرشيف الاحترافي
            var archiveButton = new Button
            {
                Text = "📁 الأرشيف\nArchive",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(180, 90),
                Location = new Point(10, 5),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            archiveButton.FlatAppearance.BorderSize = 0;
            archiveButton.Click += (s, e) => OpenArchive();

            // زر عرض المخزون
            var viewInventoryButton = new Button
            {
                Text = "📋 عرض المخزون\nInventory",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(180, 90),
                Location = new Point(200, 5),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            viewInventoryButton.FlatAppearance.BorderSize = 0;
            viewInventoryButton.Click += (s, e) => OpenInventoryView();

            // زر بيع سيارة (الأكبر في الوسط)
            var sellCarButton = new Button
            {
                Text = "🚗 بيع سيارة\nSell Car",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Size = new Size(200, 90),
                Location = new Point(390, 5),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            sellCarButton.FlatAppearance.BorderSize = 0;
            sellCarButton.Click += (s, e) => OpenSellCar();

            // زر عرض المبيعات
            var viewSalesButton = new Button
            {
                Text = "💰 عرض المبيعات\nSales",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(180, 90),
                Location = new Point(600, 5),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            viewSalesButton.FlatAppearance.BorderSize = 0;
            viewSalesButton.Click += (s, e) => OpenSalesView();

            // زر كشف حساب العميل
            var customerStatementButton = new Button
            {
                Text = "📋 كشف العميل\nStatement",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(180, 90),
                Location = new Point(790, 5),
                BackColor = Color.FromArgb(75, 0, 130),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            customerStatementButton.FlatAppearance.BorderSize = 0;
            customerStatementButton.Click += CustomerStatementButton_Click;

            // إضافة الأزرار إلى اللوحة
            buttonPanel.Controls.AddRange(new Control[]
            {
                archiveButton, viewInventoryButton, sellCarButton, viewSalesButton, customerStatementButton
            });

            mainPanel.Controls.Add(logoBox);
            mainPanel.Controls.Add(companyNameLabel);
            mainPanel.Controls.Add(welcomeLabel);
            mainPanel.Controls.Add(buttonPanel);

            // Add controls to form
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(mainPanel);
            this.Controls.Add(statusStrip);
            this.Controls.Add(menuStrip);
        }

        private void SetupMenuBasedOnPermissions()
        {
            var permissions = currentUser?.Permissions;
            if (permissions == null)
            {
                return;
            }

            // قسم المخزن - Inventory Menu
            if (permissions.CanViewInventory || permissions.CanAddCar || permissions.CanEditCar)
            {
                var inventoryMenu = CreateStyledMenuItem("المخزن");

                if (permissions.CanViewInventory)
                {
                    inventoryMenu.DropDownItems.Add(CreateStyledDropDownItem("عرض المخزون", (s, e) => OpenInventoryView()));
                }

                if (permissions.CanAddCar)
                {
                    inventoryMenu.DropDownItems.Add(CreateStyledDropDownItem("إضافة سيارة جديدة", (s, e) => OpenAddCar()));
                }

                if (permissions.CanEditCar)
                {
                    inventoryMenu.DropDownItems.Add(CreateStyledDropDownItem("تعديل سيارة", (s, e) => OpenEditCar()));
                }

                menuStrip.Items.Add(inventoryMenu);
            }

            // قسم العملاء - Customers Menu
            if (permissions.CanViewCustomers || permissions.CanAddCustomer || permissions.CanEditCustomer)
            {
                var customersMenu = CreateStyledMenuItem("العملاء");

                if (permissions.CanViewCustomers)
                {
                    customersMenu.DropDownItems.Add(CreateStyledDropDownItem("عرض العملاء", (s, e) => OpenCustomersView()));
                }

                if (permissions.CanAddCustomer)
                {
                    customersMenu.DropDownItems.Add(CreateStyledDropDownItem("إضافة عميل جديد", (s, e) => OpenAddCustomer()));
                }

                if (permissions.CanEditCustomer)
                {
                    customersMenu.DropDownItems.Add(CreateStyledDropDownItem("تعديل عميل", (s, e) => OpenEditCustomer()));
                }

                menuStrip.Items.Add(customersMenu);
            }

            // قسم الموردين - Suppliers Menu
            if (permissions.CanManageSuppliers)
            {
                var suppliersMenu = CreateStyledMenuItem("الموردين");
                suppliersMenu.DropDownItems.Add(CreateStyledDropDownItem("إدارة الموردين", (s, e) => OpenSuppliersManagement()));
                menuStrip.Items.Add(suppliersMenu);
            }

            // قسم المبيعات - Sales Menu
            if (permissions.CanSell || permissions.CanViewSales)
            {
                var salesMenu = CreateStyledMenuItem("المبيعات");

                if (permissions.CanSell)
                {
                    salesMenu.DropDownItems.Add(CreateStyledDropDownItem("بيع سيارة", (s, e) => OpenSellCar()));
                }

                if (permissions.CanViewSales)
                {
                    salesMenu.DropDownItems.Add(CreateStyledDropDownItem("عرض المبيعات", (s, e) => OpenSalesView()));
                }

                menuStrip.Items.Add(salesMenu);
            }

            // قسم الحسابات - Accounting Menu
            if (permissions.CanViewAccounts)
            {
                var accountingMenu = CreateStyledMenuItem("الحسابات");
                accountingMenu.DropDownItems.Add(CreateStyledDropDownItem("عرض الحسابات", (s, e) => OpenAccountingView()));
                menuStrip.Items.Add(accountingMenu);
            }

            // قسم التقارير - Reports Menu
            if (permissions.CanViewGeneralReports || permissions.CanViewCustomerReport || permissions.CanPrintReports || permissions.CanPrintStatements)
            {
                var reportsMenu = CreateStyledMenuItem("التقارير");

                if (permissions.CanViewGeneralReports)
                {
                    reportsMenu.DropDownItems.Add(CreateStyledDropDownItem("التقارير العامة", (s, e) => OpenGeneralReports()));
                }

                if (permissions.CanViewCustomerReport)
                {
                    reportsMenu.DropDownItems.Add(CreateStyledDropDownItem("تقارير العملاء", (s, e) => OpenCustomerReports()));
                    reportsMenu.DropDownItems.Add(CreateStyledDropDownItem("كشف حساب العميل", (s, e) => OpenCustomerStatement()));
                }
                if (permissions.CanPrintReports || permissions.CanPrintStatements)
                {
                    reportsMenu.DropDownItems.Add(new ToolStripSeparator());
                    reportsMenu.DropDownItems.Add(CreateStyledDropDownItem("خيارات الطباعة", (s, e) => PrintButton_Click(s, e)));
                }

                menuStrip.Items.Add(reportsMenu);
            }

            // قسم الإدارة - Management Menu (للمطور والمدير)
            if (permissions.CanManageUsers || permissions.CanManageSettings || permissions.CanActivateInstallation)
            {
                var managementMenu = CreateStyledMenuItem("الإدارة");

                if (permissions.CanManageUsers)
                {
                    managementMenu.DropDownItems.Add(CreateStyledDropDownItem("إدارة المستخدمين", (s, e) => OpenUserManagement()));
                }

                if (permissions.CanManageSettings)
                {
                    managementMenu.DropDownItems.Add(CreateStyledDropDownItem("الإعدادات", (s, e) => OpenSettings()));
                }

                if (permissions.CanActivateInstallation)
                {
                    managementMenu.DropDownItems.Add(new ToolStripSeparator());
                    managementMenu.DropDownItems.Add(CreateStyledDropDownItem("تفعيل التثبيت", (s, e) => ToggleInstallationActivation()));
                    managementMenu.DropDownItems.Add(CreateStyledDropDownItem("حالة التفعيل", (s, e) => CheckInstallationStatus()));
                }

                // Developer-only features
                if (currentUser?.Role == UserRole.Developer)
                {
                    managementMenu.DropDownItems.Add(new ToolStripSeparator());
                    managementMenu.DropDownItems.Add(CreateStyledDropDownItem("ضبط المصنع", (s, e) => FactoryReset()));
                }

                menuStrip.Items.Add(managementMenu);
            }

            // قائمة النظام - System Menu
            var systemMenu = CreateStyledMenuItem("النظام");
            systemMenu.DropDownItems.Add(CreateStyledDropDownItem("تسجيل الخروج", (s, e) => Logout()));
            systemMenu.DropDownItems.Add(CreateStyledDropDownItem("إنهاء البرنامج", (s, e) => ExitApplication()));
            menuStrip.Items.Add(systemMenu);
        }

        // This method is no longer needed as authentication is now enforced.
        // It will be removed or refactored if a non-authenticated mode is required in the future.
        private void SetupAllMenus()
        {
            // This method is intentionally left empty or removed as it's no longer used.
        }

        // Helper methods for creating styled menu items
        private ToolStripMenuItem CreateStyledMenuItem(string text)
        {
            var menuItem = new ToolStripMenuItem(text)
            {
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(52, 58, 64),
                Padding = new Padding(15, 8, 15, 8),
                Margin = new Padding(5, 2, 5, 2)
            };

            return menuItem;
        }

        private ToolStripMenuItem CreateStyledDropDownItem(string text, EventHandler clickHandler)
        {
            var dropDownItem = new ToolStripMenuItem(text)
            {
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(52, 58, 64),
                Padding = new Padding(20, 10, 20, 10),
                Margin = new Padding(2)
            };

            dropDownItem.Click += clickHandler;
            return dropDownItem;
        }

        private void ShowLicenseInfo()
        {
            try
            {
                var licenseInfo = LicenseManager.GetLicenseInfo();

                // عرض معلومات الترخيص في شريط الحالة
                if (statusStrip != null && statusStrip.Items.Count > 0)
                {
                    var statusLabel = statusStrip.Items[0] as ToolStripStatusLabel;
                    if (statusLabel != null)
                    {
                        statusLabel.Text = $"حالة الترخيص: {licenseInfo}";
                    }
                }

                // للمدير فقط: عرض تنبيه إذا كان الترخيص غير مُفعل
                if (currentUser.Role == UserRole.Manager)
                {
                    var licenseResult = LicenseManager.CheckLicense();
                    if (!licenseResult.IsValid)
                    {
                        var result = MessageBox.Show($"{licenseResult.Message}\n\n" +
                            "هل تريد تشغيل فحص النظام؟\n\n" +
                            "سيتم فتح أداة فحص النظام حيث ستحتاج إلى:\n" +
                            "1. إدخال رمز التحقق من النظام\n" +
                            "2. اختيار إعدادات النظام\n" +
                            "3. إكمال عملية الإعداد",
                            "فحص النظام", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                        if (result == DialogResult.Yes)
                        {
                            try
                            {
                                var batchPath = Path.Combine(Application.StartupPath, "activate.bat");
                                if (File.Exists(batchPath))
                                {
                                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                                    {
                                        FileName = batchPath,
                                        UseShellExecute = true,
                                        Verb = "runas"
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("لم يتم العثور على أداة فحص النظام!", "خطأ",
                                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"خطأ في تشغيل أداة فحص النظام:\n{ex.Message}", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, false, "خطأ في عرض معلومات الترخيص");
            }
        }

        private async void UpdateDatabaseAsync()
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                await context.EnsureDatabaseUpdatedAsync();
            }, "تحديث قاعدة البيانات");
        }

        private void LoadCompanySettings()
        {
            // Attempt to load settings from database
            ErrorHandlingService.TryExecute(async () =>
            {
                systemSettings = await context.SystemSettings.FirstOrDefaultAsync();
                if (systemSettings != null)
                {
                    companyNameLabel.Text = systemSettings.CompanyName ?? "معرض السيارات";

                    // Load logo if exists
                    if (!string.IsNullOrEmpty(systemSettings.CompanyLogo))
                    {
                        string logoPath = Path.Combine(Application.StartupPath, systemSettings.CompanyLogo);
                        if (File.Exists(logoPath))
                        {
                            logoBox.Image?.Dispose();
                            logoBox.Image = Image.FromFile(logoPath);
                        }
                    }
                }
            }, "تحميل إعدادات الشركة");
        }

        private string GetRoleDisplayName(UserRole role)
        {
            return role switch
            {
                UserRole.Developer => "مطور النظام",
                UserRole.Manager => "مدير النشاط",
                UserRole.SalesRepresentative => "مندوب مبيعات",
                _ => "غير محدد"
            };
        }

        // Menu event handlers
        private void OpenInventoryView()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var inventoryForm = new InventoryForm();
                inventoryForm.ShowDialog();
            }, "فتح شاشة المخزون");
        }

        private void OpenAddCar()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var addCarForm = new AddEditCarForm();
                addCarForm.ShowDialog();
            }, "فتح شاشة إضافة سيارة");
        }

        private void OpenEditCar()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var inventoryForm = new InventoryForm();
                inventoryForm.ShowDialog();
            }, "فتح شاشة تعديل سيارة");
        }

        private void OpenCustomersView()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var customerForm = new CustomerForm(currentUser);
                customerForm.ShowDialog();
            }, "فتح شاشة العملاء");
        }

        private void OpenAddCustomer()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var addCustomerForm = new AddEditCustomerForm();
                addCustomerForm.ShowDialog();
            }, "فتح شاشة إضافة عميل");
        }

        private void OpenEditCustomer()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var customerForm = new CustomerForm(currentUser);
                customerForm.ShowDialog();
            }, "فتح شاشة تعديل عميل");
        }

        private void OpenSuppliersManagement()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var supplierForm = new SupplierForm();
                supplierForm.ShowDialog();
            }, "فتح شاشة إدارة الموردين");
        }

        private void OpenSellCar()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var salesForm = new SalesForm(currentUser);
                salesForm.ShowDialog();
            }, "فتح شاشة بيع سيارة");
        }

        private void OpenSalesView()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var salesViewForm = new SalesViewForm();
                salesViewForm.ShowDialog();
            }, "فتح شاشة عرض المبيعات");
        }

        private void OpenAccountingView()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var accountingForm = new AccountingForm();
                accountingForm.ShowDialog();
            }, "فتح شاشة الحسابات");
        }

        private void OpenGeneralReports()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var reportsForm = new ReportsForm();
                reportsForm.ShowDialog();
            }, "فتح شاشة التقارير العامة");
        }

        private void OpenCustomerReports()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var reportsForm = new ReportsForm();
                reportsForm.ShowDialog();
            }, "فتح شاشة تقارير العملاء");
        }

        private void OpenCustomerStatement()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                // This will likely need a way to select a customer first
                // For now, we'll open a form that allows selection or pass a dummy ID
                var customerSelectionForm = new CustomerSelectionFormForStatement(); // A new form to select customer
                if (customerSelectionForm.ShowDialog() == DialogResult.OK)
                {
                    var customerId = customerSelectionForm.SelectedCustomerId;
                    var customerStatementForm = new CustomerStatementForm(customerId);
                    customerStatementForm.ShowDialog();
                }
            }, "فتح كشف حساب العميل");
        }

        private void OpenUserManagement()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var userManagementForm = new UserManagementForm(currentUser);
                userManagementForm.ShowDialog();
            }, "فتح شاشة إدارة المستخدمين");
        }

        private void OpenSettings()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var settingsForm = new SettingsForm();
                settingsForm.ShowDialog();
            }, "فتح شاشة الإعدادات");
        }

        private void Logout()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                // Close current form and open login form
                this.Hide();
                var loginForm = new LoginForm();
                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    // If login successful, open new MainDashboard with new user
                    currentUser = loginForm.AuthenticatedUser;
                    InitializeComponent(); // Re-initialize components for new user
                    SetupMenuBasedOnPermissions(); // Setup menus based on new user's permissions
                    this.Show();
                }
                else
                {
                    // If login cancelled or failed, exit application
                    Application.Exit();
                }
            }, "تسجيل الخروج");
        }

        private void ExitApplication()
        {
            ErrorHandlingService.TryExecute(() =>
            {
                var result = MessageBox.Show("هل أنت متأكد من إنهاء البرنامج؟", "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    Application.Exit();
                }
            }, "إنهاء التطبيق");
        }

        // Installation activation methods
        private async void ToggleInstallationActivation()
        {
            try
            {
                var currentStatus = await installationService.IsInstallationAuthorizedAsync();

                if (currentStatus)
                {
                    var result = MessageBox.Show("هل تريد إلغاء تفعيل التثبيت؟\nسيتم إيقاف البرنامج على الأجهزة الأخرى.",
                                                 "تأكيد إلغاء التفعيل", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        await installationService.DeactivateInstallationAsync(currentUser!, "تم إلغاء التفعيل بواسطة المستخدم");
                        MessageBox.Show("تم إلغاء تفعيل التثبيت بنجاح.", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    var result = MessageBox.Show("هل تريد تفعيل التثبيت؟\nسيسمح هذا بتشغيل البرنامج على أجهزة أخرى.",
                                                 "تأكيد التفعيل", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        await installationService.ActivateInstallationAsync(currentUser!, "تم التفعيل بواسطة المستخدم");
                        MessageBox.Show("تم تفعيل التثبيت بنجاح.", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, true, "حدث خطأ أثناء تفعيل/إلغاء تفعيل التثبيت.");
            }
        }

        private async void CheckInstallationStatus()
        {
            try
            {
                var isActive = await installationService.IsInstallationAuthorizedAsync();
                var deviceId = installationService.GetDeviceId();

                var status = isActive ? "مفعل" : "غير مفعل";
                var message = $"حالة التفعيل: {status}\nمعرف الجهاز: {deviceId}";

                MessageBox.Show(message, "حالة التثبيت", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, true, "حدث خطأ أثناء فحص حالة التثبيت.");
            }
        }

        private void PrintButton_Click(object sender, EventArgs e)
        {
            ErrorHandlingService.TryExecute(() =>
            {
                // Show print options form or menu
                var printOptionsForm = new PrintOptionsForm();
                printOptionsForm.ShowDialog();
            }, "فتح خيارات الطباعة");
        }

        // معالج حدث زر كشف حساب العميل
        private void CustomerStatementButton_Click(object sender, EventArgs e)
        {
            try
            {
                // فتح نموذج اختيار العميل
                var customerSelectionForm = new CustomerSelectionForm();
                if (customerSelectionForm.ShowDialog() == DialogResult.OK)
                {
                    var selectedCustomerId = customerSelectionForm.SelectedCustomerId;
                    var selectedCustomerName = customerSelectionForm.SelectedCustomerName;

                    if (selectedCustomerId > 0)
                    {
                        var customerStatementForm = new CustomerStatementForm(selectedCustomerId);
                        customerStatementForm.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح كشف حساب العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // معالج حدث زر الأرشيف الاحترافي
        private void OpenArchive()
        {
            try
            {
                var archiveForm = new ProfessionalArchiveForm();
                archiveForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح الأرشيف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FactoryReset()
        {
            var result = MessageBox.Show(
                "تحذير: ضبط المصنع سيقوم بما يلي:\n\n" +
                "• حذف جميع البيانات (السيارات، العملاء، المبيعات، الأقساط)\n" +
                "• حذف جميع المستخدمين عدا المطور الافتراضي\n" +
                "• إعادة تعيين كلمة المرور الافتراضية\n" +
                "• حذف جميع الملفات المرفوعة\n" +
                "• إعادة تعيين إعدادات النظام\n\n" +
                "هذا الإجراء لا يمكن التراجع عنه!\n\n" +
                "هل أنت متأكد من المتابعة؟",
                "تأكيد ضبط المصنع",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result != DialogResult.Yes)
                return;

            // Second confirmation
            var secondResult = MessageBox.Show(
                "تأكيد نهائي: هل أنت متأكد 100% من ضبط المصنع؟\n\n" +
                "سيتم فقدان جميع البيانات نهائياً!",
                "تأكيد نهائي",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Stop);

            if (secondResult != DialogResult.Yes)
                return;

            try
            {
                // Show progress message
                var progressForm = new Form
                {
                    Text = "ضبط المصنع",
                    Size = new Size(400, 150),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                var progressLabel = new Label
                {
                    Text = "جاري تنفيذ ضبط المصنع...\nيرجى الانتظار...",
                    TextAlign = ContentAlignment.MiddleCenter,
                    Dock = DockStyle.Fill,
                    Font = new Font("Segoe UI", 12F)
                };

                progressForm.Controls.Add(progressLabel);
                progressForm.Show();
                Application.DoEvents();

                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                // Delete all data
                context.InstallmentPayments.RemoveRange(context.InstallmentPayments);
                context.Sales.RemoveRange(context.Sales);
                context.Cars.RemoveRange(context.Cars);
                context.Customers.RemoveRange(context.Customers);
                context.Suppliers.RemoveRange(context.Suppliers);
                context.SupplierPayments.RemoveRange(context.SupplierPayments);

                // Delete system settings
                context.SystemSettings.RemoveRange(context.SystemSettings);

                // Delete all users except developer
                var usersToDelete = context.Users.Where(u => u.Username != "amrali").ToList();
                var permissionsToDelete = context.UserPermissions.Where(up =>
                    usersToDelete.Any(u => u.UserId == up.UserId)).ToList();

                context.UserPermissions.RemoveRange(permissionsToDelete);
                context.Users.RemoveRange(usersToDelete);

                // Reset developer password
                var developer = context.Users.FirstOrDefault(u => u.Username == "amrali");
                if (developer != null)
                {
                    developer.PasswordHash = BCrypt.Net.BCrypt.HashPassword("braa");
                }

                // Create default system settings
                var defaultSettings = new SystemSettings
                {
                    CompanyName = "معرض السيارات",
                    CompanyAddress = "",
                    CompanyPhone = "",
                    CommercialRegister = "",
                    TaxCard = "",
                    Currency = "ريال سعودي",
                    SubscriptionType = SubscriptionType.Yearly,
                    SubscriptionStartDate = DateTime.Now,
                    SubscriptionEndDate = DateTime.Now.AddYears(1),
                    IsActive = true,
                    AutoBackupEnabled = false,
                    BackupIntervalHours = 24,
                    BackupPath = "",
                    CreatedDate = DateTime.Now
                };
                context.SystemSettings.Add(defaultSettings);

                progressLabel.Text = "جاري حفظ التغييرات...";
                Application.DoEvents();
                context.SaveChanges();

                progressLabel.Text = "جاري حذف الملفات المرفوعة...";
                Application.DoEvents();

                // Delete uploaded files
                try
                {
                    var archiveDir = Path.Combine(Application.StartupPath, "Archive");
                    if (Directory.Exists(archiveDir))
                    {
                        Directory.Delete(archiveDir, true);
                    }
                }
                catch { /* Ignore file deletion errors */ }

                progressLabel.Text = "تم الانتهاء من ضبط المصنع!";
                Application.DoEvents();
                System.Threading.Thread.Sleep(1000);

                progressForm.Close();
                progressForm.Dispose();

                MessageBox.Show(
                    "✅ تم ضبط المصنع بنجاح!\n\n" +
                    "📋 ما تم تنفيذه:\n" +
                    "• حذف جميع البيانات (السيارات، العملاء، المبيعات، الأقساط)\n" +
                    "• حذف جميع المستخدمين عدا المطور الافتراضي\n" +
                    "• إعادة تعيين كلمة المرور الافتراضية\n" +
                    "• حذف جميع الملفات المرفوعة\n" +
                    "• إعادة تعيين إعدادات النظام\n\n" +
                    "🔑 بيانات تسجيل الدخول:\n" +
                    "اسم المستخدم: amrali\n" +
                    "كلمة المرور: braa\n\n" +
                    "🔄 سيتم إعادة تشغيل البرنامج الآن.",
                    "ضبط المصنع مكتمل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // Restart application
                Application.Restart();
            }
            catch (Exception ex)
            {
                // Close progress form if it exists
                try
                {
                    var progressForms = Application.OpenForms.Cast<Form>().Where(f => f.Text == "ضبط المصنع").ToList();
                    foreach (var form in progressForms)
                    {
                        form.Close();
                        form.Dispose();
                    }
                }
                catch { /* Ignore cleanup errors */ }

                MessageBox.Show(
                    $"❌ حدث خطأ أثناء ضبط المصنع:\n\n" +
                    $"تفاصيل الخطأ: {ex.Message}\n\n" +
                    $"يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
                    "خطأ في ضبط المصنع",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);

                // Log the error
                ErrorHandlingService.HandleException(ex, false, "خطأ في ضبط المصنع");
            }
        }
    }

    // Custom color table for professional menu styling
    public class CustomColorTable : ProfessionalColorTable
    {
        public override Color MenuItemSelected => Color.FromArgb(73, 80, 87);
        public override Color MenuItemSelectedGradientBegin => Color.FromArgb(73, 80, 87);
        public override Color MenuItemSelectedGradientEnd => Color.FromArgb(73, 80, 87);
        public override Color MenuItemPressedGradientBegin => Color.FromArgb(108, 117, 125);
        public override Color MenuItemPressedGradientEnd => Color.FromArgb(108, 117, 125);
        public override Color MenuItemBorder => Color.FromArgb(52, 58, 64);
        public override Color ToolStripDropDownBackground => Color.FromArgb(52, 58, 64);
        public override Color ImageMarginGradientBegin => Color.FromArgb(52, 58, 64);
        public override Color ImageMarginGradientMiddle => Color.FromArgb(52, 58, 64);
        public override Color ImageMarginGradientEnd => Color.FromArgb(52, 58, 64);
        public override Color MenuBorder => Color.FromArgb(52, 58, 64);
        public override Color MenuStripGradientBegin => Color.FromArgb(52, 58, 64);
        public override Color MenuStripGradientEnd => Color.FromArgb(52, 58, 64);
    }
}
