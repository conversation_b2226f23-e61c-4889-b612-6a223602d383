using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace CarDealershipManagement.Forms
{
    public partial class AddEditCustomerForm : Form
    {
        private TextBox txtFullName;
        private TextBox txtIdNumber;
        private DateTimePicker dtpDateOfBirth;
        private TextBox txtCountry;
        private TextBox txtCity;
        private TextBox txtArea;
        private TextBox txtStreet;
        private TextBox txtPrimaryPhone;
        private TextBox txtSecondaryPhone;
        private TextBox txtEmail;
        private Button btnSave;
        private Button btnCancel;
        private Label lblStatus;

        private int? customerId;

        public AddEditCustomerForm()
        {
            InitializeComponent();
        }

        public AddEditCustomerForm(int customerId)
        {
            this.customerId = customerId;
            InitializeComponent();
            LoadCustomerData(customerId);
        }

[MemberNotNull(nameof(txtFullName), nameof(txtIdNumber), nameof(dtpDateOfBirth), nameof(txtCountry), nameof(txtCity), nameof(txtArea), nameof(txtStreet), nameof(txtPrimaryPhone), nameof(txtSecondaryPhone), nameof(txtEmail), nameof(btnSave), nameof(btnCancel), nameof(lblStatus))]
        private void InitializeComponent()
        {
            this.Text = customerId == null ? "👤 إضافة عميل جديد" : "✏️ تعديل بيانات العميل";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Font = new Font("Segoe UI", 11F);
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimumSize = new Size(900, 700);

            // إنشاء لوحة رئيسية مع تمرير
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(50)
            };

            // عنوان النموذج
            var titleLabel = new Label
            {
                Text = customerId == null ? "👤 إضافة عميل جديد" : "✏️ تعديل بيانات العميل",
                Location = new Point(50, 20),
                Size = new Size(700, 40),
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Common styling for labels - محسن للشاشة الكاملة
            Action<Label> styleLabel = (lbl) =>
            {
                lbl.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                lbl.ForeColor = Color.FromArgb(52, 58, 64);
                lbl.AutoSize = true;
            };

            // Common styling for textboxes - محسن للشاشة الكاملة
            Action<Control> styleInput = (ctrl) =>
            {
                ctrl.Font = new Font("Segoe UI", 11F);
                ctrl.BackColor = Color.White;
                ctrl.ForeColor = Color.Black;
                if (ctrl is TextBox || ctrl is DateTimePicker)
                {
                    ctrl.Height = 35;
                }
            };

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(50, 600),
                Size = new Size(800, 30),
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // الصف الأول - الاسم الكامل
            var lblFullName = new Label { Text = "👤 الاسم الكامل:", Location = new Point(50, 80) };
            styleLabel(lblFullName);
            txtFullName = new TextBox { Location = new Point(200, 78), Size = new Size(500, 35) };
            styleInput(txtFullName);

            // الصف الثاني - رقم الهوية وتاريخ الميلاد
            var lblIdNumber = new Label { Text = "🆔 رقم الهوية:", Location = new Point(50, 140) };
            styleLabel(lblIdNumber);
            txtIdNumber = new TextBox { Location = new Point(200, 138), Size = new Size(250, 35) };
            styleInput(txtIdNumber);

            var lblDateOfBirth = new Label { Text = "📅 تاريخ الميلاد:", Location = new Point(500, 140) };
            styleLabel(lblDateOfBirth);
            dtpDateOfBirth = new DateTimePicker { Location = new Point(650, 138), Size = new Size(200, 35), Format = DateTimePickerFormat.Short };
            styleInput(dtpDateOfBirth);

            // الصف الثالث - الدولة والمدينة
            var lblCountry = new Label { Text = "🌍 الدولة:", Location = new Point(50, 200) };
            styleLabel(lblCountry);
            txtCountry = new TextBox { Location = new Point(200, 198), Size = new Size(250, 35) };
            styleInput(txtCountry);

            var lblCity = new Label { Text = "🏙️ المدينة:", Location = new Point(500, 200) };
            styleLabel(lblCity);
            txtCity = new TextBox { Location = new Point(600, 198), Size = new Size(250, 35) };
            styleInput(txtCity);

            // الصف الرابع - المنطقة والشارع
            var lblArea = new Label { Text = "📍 المنطقة:", Location = new Point(50, 260) };
            styleLabel(lblArea);
            txtArea = new TextBox { Location = new Point(200, 258), Size = new Size(250, 35) };
            styleInput(txtArea);

            var lblStreet = new Label { Text = "🛣️ الشارع:", Location = new Point(500, 260) };
            styleLabel(lblStreet);
            txtStreet = new TextBox { Location = new Point(600, 258), Size = new Size(250, 35) };
            styleInput(txtStreet);

            // الصف الخامس - الهواتف
            var lblPrimaryPhone = new Label { Text = "📞 الهاتف الأساسي:", Location = new Point(50, 320) };
            styleLabel(lblPrimaryPhone);
            txtPrimaryPhone = new TextBox { Location = new Point(200, 318), Size = new Size(250, 35) };
            styleInput(txtPrimaryPhone);

            var lblSecondaryPhone = new Label { Text = "📱 الهاتف الثانوي:", Location = new Point(500, 320) };
            styleLabel(lblSecondaryPhone);
            txtSecondaryPhone = new TextBox { Location = new Point(650, 318), Size = new Size(200, 35) };
            styleInput(txtSecondaryPhone);

            // الصف السادس - البريد الإلكتروني
            var lblEmail = new Label { Text = "📧 البريد الإلكتروني:", Location = new Point(50, 380) };
            styleLabel(lblEmail);
            txtEmail = new TextBox { Location = new Point(200, 378), Size = new Size(400, 35) };
            styleInput(txtEmail);

            // لوحة الأزرار
            var buttonPanel = new Panel
            {
                Location = new Point(50, 460),
                Size = new Size(800, 80),
                BackColor = Color.Transparent
            };

            // Save Button - محسن
            btnSave = new Button
            {
                Text = "💾 حفظ",
                Location = new Point(250, 20),
                Size = new Size(150, 45),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.FlatAppearance.MouseOverBackColor = Color.FromArgb(34, 139, 58);
            btnSave.Click += BtnSave_Click;

            // Cancel Button - محسن
            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Location = new Point(450, 20),
                Size = new Size(150, 45),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.FlatAppearance.MouseOverBackColor = Color.FromArgb(200, 35, 51);
            btnCancel.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });

            // إضافة العناصر للوحة الرئيسية
            mainPanel.Controls.AddRange(new Control[]
            {
                titleLabel, lblFullName, txtFullName, lblIdNumber, txtIdNumber,
                lblDateOfBirth, dtpDateOfBirth, lblCountry, txtCountry,
                lblCity, txtCity, lblArea, txtArea, lblStreet, txtStreet,
                lblPrimaryPhone, txtPrimaryPhone, lblSecondaryPhone, txtSecondaryPhone,
                lblEmail, txtEmail, buttonPanel, lblStatus
            });

            // إضافة اللوحة الرئيسية للنموذج
            this.Controls.Add(mainPanel);
        }

        private async void LoadCustomerData(int customerId)
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                var customer = await context.Customers.FindAsync(customerId);

                if (customer != null)
                {
                    txtFullName.Text = customer.FullName;
                    txtIdNumber.Text = customer.IdNumber;
                    dtpDateOfBirth.Value = customer.DateOfBirth;
                    txtCountry.Text = customer.Country;
                    txtCity.Text = customer.City;
                    txtArea.Text = customer.Area;
                    txtStreet.Text = customer.Street;
                    txtPrimaryPhone.Text = customer.PrimaryPhone;
                    txtSecondaryPhone.Text = customer.SecondaryPhone ?? string.Empty;
                    txtEmail.Text = customer.Email ?? string.Empty;
                }
                else
                {
                    lblStatus.Text = "العميل غير موجود.";
                    lblStatus.ForeColor = Color.Red;
                    this.Close();
                }
            }, "تحميل بيانات العميل");
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            // Manual validation for required fields
            if (string.IsNullOrWhiteSpace(txtFullName.Text) ||
                string.IsNullOrWhiteSpace(txtIdNumber.Text) ||
                string.IsNullOrWhiteSpace(txtCountry.Text) ||
                string.IsNullOrWhiteSpace(txtCity.Text) ||
                string.IsNullOrWhiteSpace(txtArea.Text) ||
                string.IsNullOrWhiteSpace(txtStreet.Text) ||
                string.IsNullOrWhiteSpace(txtPrimaryPhone.Text))
            {
                lblStatus.Text = "يرجى إدخال جميع البيانات المطلوبة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Validate phone numbers using regex or built-in validation if available
            if (!string.IsNullOrEmpty(txtPrimaryPhone.Text) && !IsValidPhoneNumber(txtPrimaryPhone.Text))
            {
                lblStatus.Text = "صيغة رقم الهاتف الأساسي غير صحيحة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }
            if (!string.IsNullOrEmpty(txtSecondaryPhone.Text) && !IsValidPhoneNumber(txtSecondaryPhone.Text))
            {
                lblStatus.Text = "صيغة رقم الهاتف الثانوي غير صحيحة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Validate email
            if (!string.IsNullOrEmpty(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                lblStatus.Text = "صيغة البريد الإلكتروني غير صحيحة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                Customer customer;

                if (customerId == null)  // New customer
                {
                    // Check if ID number already exists
                    if (await context.Customers.AnyAsync(c => c.IdNumber == txtIdNumber.Text))
                    {
                        lblStatus.Text = "يوجد عميل آخر بنفس رقم الهوية. يرجى إدخال رقم هوية فريد.";
                        lblStatus.ForeColor = Color.Red;
                        return;
                    }

                    customer = new Customer();
                    context.Customers.Add(customer);
                }
                else // Existing customer
                {
                    customer = await context.Customers.FindAsync(customerId);
                    if (customer == null)
                    {
                        lblStatus.Text = "العميل غير موجود.";
                        lblStatus.ForeColor = Color.Red;
                        return;
                    }

                    // Check if ID number is changed and already exists
                    if (customer.IdNumber != txtIdNumber.Text)
                    {
                        if (await context.Customers.AnyAsync(c => c.IdNumber == txtIdNumber.Text && c.CustomerId != customerId))
                        {
                            lblStatus.Text = "يوجد عميل آخر بنفس رقم الهوية. يرجى إدخال رقم هوية فريد.";
                            lblStatus.ForeColor = Color.Red;
                            return;
                        }
                    }
                }

                customer.FullName = txtFullName.Text;
                customer.IdNumber = txtIdNumber.Text;
                customer.DateOfBirth = dtpDateOfBirth.Value;
                customer.Country = txtCountry.Text;
                customer.City = txtCity.Text;
                customer.Area = txtArea.Text;
                customer.Street = txtStreet.Text;
                customer.PrimaryPhone = txtPrimaryPhone.Text;
                customer.SecondaryPhone = string.IsNullOrWhiteSpace(txtSecondaryPhone.Text) ? null : txtSecondaryPhone.Text;
                customer.Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text;
                customer.ModifiedDate = DateTime.Now;

                await context.SaveChangesAsync();
                MessageBox.Show("تم حفظ العميل بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }, "حفظ بيانات العميل");
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            // A simple regex for phone number validation. 
            // This can be made more robust based on specific regional requirements.
            return System.Text.RegularExpressions.Regex.IsMatch(phoneNumber, @"^\+?[0-9]{7,15}$");
        }
    }
}


