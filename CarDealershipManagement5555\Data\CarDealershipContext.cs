using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;

namespace CarDealershipManagement.Data
{
    public class CarDealershipContext : DbContext
    {
        public CarDealershipContext(DbContextOptions<CarDealershipContext> options) : base(options)
        {
        }

        // Main entities
        public DbSet<Car> Cars
        {
            get;
            set;
        }
        public DbSet<Customer> Customers
        {
            get;
            set;
        }
        public DbSet<Supplier> Suppliers
        {
            get;
            set;
        }
        public DbSet<Sale> Sales
        {
            get;
            set;
        }
        public DbSet<InstallmentPayment> InstallmentPayments
        {
            get;
            set;
        }

        // File and document entities
        public DbSet<CarImage> CarImages
        {
            get;
            set;
        }
        public DbSet<CarDocument> CarDocuments
        {
            get;
            set;
        }
        public DbSet<CustomerDocument> CustomerDocuments
        {
            get;
            set;
        }
        public DbSet<SupplierDocument> SupplierDocuments
        {
            get;
            set;
        }
        public DbSet<SupplierPayment> SupplierPayments
        {
            get;
            set;
        }
        public DbSet<SupplierPaymentSchedule> SupplierPaymentSchedules
        {
            get;
            set;
        }

        // User and system entities
        public DbSet<User> Users
        {
            get;
            set;
        }
        public DbSet<UserPermissions> UserPermissions
        {
            get;
            set;
        }
        public DbSet<SystemSettings> SystemSettings
        {
            get;
            set;
        }
        public DbSet<AuthorizedInstallation> AuthorizedInstallations
        {
            get;
            set;
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                ErrorHandlingService.TryExecute(() =>
                {
                    optionsBuilder.UseSqlite("Data Source=CarDealership.db");
                }, "Database Configuration");
            }
        }

        public async Task EnsureDatabaseUpdatedAsync()
        {
            try
            {
                // تحديث قاعدة البيانات إذا لزم الأمر
                await Database.EnsureCreatedAsync();

                // التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
                var connection = Database.GetDbConnection();
                await connection.OpenAsync();

                try
                {
                    // التحقق من وجود عمود CommercialRegister
                    var checkCommercialRegisterCommand = connection.CreateCommand();
                    checkCommercialRegisterCommand.CommandText = "PRAGMA table_info(SystemSettings)";

                    bool hasCommercialRegister = false;
                    bool hasTaxCard = false;

                    using (var reader = await checkCommercialRegisterCommand.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var columnName = reader.GetString(1);
                            if (columnName == "CommercialRegister")
                                hasCommercialRegister = true;
                            if (columnName == "TaxCard")
                                hasTaxCard = true;
                        }
                    }

                    // إضافة الأعمدة المفقودة
                    if (!hasCommercialRegister)
                    {
                        var addCommercialRegisterCommand = connection.CreateCommand();
                        addCommercialRegisterCommand.CommandText = "ALTER TABLE SystemSettings ADD COLUMN CommercialRegister TEXT DEFAULT ''";
                        await addCommercialRegisterCommand.ExecuteNonQueryAsync();
                    }

                    if (!hasTaxCard)
                    {
                        var addTaxCardCommand = connection.CreateCommand();
                        addTaxCardCommand.CommandText = "ALTER TABLE SystemSettings ADD COLUMN TaxCard TEXT DEFAULT ''";
                        await addTaxCardCommand.ExecuteNonQueryAsync();
                    }
                }
                finally
                {
                    await connection.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, true, "خطأ في تحديث قاعدة البيانات");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Car entity configuration
            modelBuilder.Entity<Car>(entity =>
            {
                entity.HasKey(e => e.ChassisNumber);
                entity.Property(e => e.ChassisNumber).ValueGeneratedNever();
                entity.HasIndex(e => e.ChassisNumber).IsUnique();
            });

            // Customer entity configuration
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasIndex(e => e.IdNumber).IsUnique();
                // Global query filter for soft delete
                entity.HasQueryFilter(c => !c.IsDeleted);
            });

            // Sale entity configuration
            modelBuilder.Entity<Sale>(entity =>
            {
                entity.HasOne(s => s.Car)
                .WithOne(c => c.Sale)
                .HasForeignKey<Sale>(s => s.CarChassisNumber)
                .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(s => s.Customer)
                .WithMany(c => c.Sales)
                .HasForeignKey(s => s.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);
            });

            // InstallmentPayment entity configuration
            modelBuilder.Entity<InstallmentPayment>(entity =>
            {
                entity.HasOne(ip => ip.Sale)
                .WithMany(s => s.InstallmentPayments)
                .HasForeignKey(ip => ip.SaleId)
                .OnDelete(DeleteBehavior.Cascade);
            });

            // User and UserPermissions relationship
            modelBuilder.Entity<UserPermissions>(entity =>
            {
                entity.HasOne(up => up.User)
                .WithOne(u => u.Permissions)
                .HasForeignKey<UserPermissions>(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);
            });

            // File relationships
            modelBuilder.Entity<CarImage>(entity =>
            {
                entity.HasOne(ci => ci.Car)
                .WithMany(c => c.Images)
                .HasForeignKey(ci => ci.CarChassisNumber)
                .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<CarDocument>(entity =>
            {
                entity.HasOne(cd => cd.Car)
                .WithMany(c => c.Documents)
                .HasForeignKey(cd => cd.CarChassisNumber)
                .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<CustomerDocument>(entity =>
            {
                entity.HasOne(cd => cd.Customer)
                .WithMany(c => c.Documents)
                .HasForeignKey(cd => cd.CustomerId)
                .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<SupplierDocument>(entity =>
            {
                entity.HasOne(sd => sd.Supplier)
                .WithMany(s => s.Documents)
                .HasForeignKey(sd => sd.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<SupplierPayment>(entity =>
            {
                entity.HasOne(sp => sp.Supplier)
                .WithMany(s => s.Payments)
                .HasForeignKey(sp => sp.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<SupplierPaymentSchedule>(entity =>
            {
                entity.HasKey(sps => sps.ScheduleId);
                entity.HasOne(sps => sps.Supplier)
                .WithMany(s => s.PaymentSchedules)
                .HasForeignKey(sps => sps.SupplierId)
                .OnDelete(DeleteBehavior.Cascade);
                entity.Property(sps => sps.Description).HasMaxLength(500);
                entity.Property(sps => sps.PaymentStatus).HasDefaultValue(PaymentStatus.Pending);
            });

            // AuthorizedInstallation configuration
            modelBuilder.Entity<AuthorizedInstallation>(entity =>
            {
                entity.HasKey(ai => ai.InstallationId);
                entity.HasIndex(ai => ai.DeviceId).IsUnique();
                entity.HasIndex(ai => ai.ActivationCode).IsUnique();
                entity.Property(ai => ai.DeviceId).IsRequired().HasMaxLength(100);
                entity.Property(ai => ai.ActivationCode).IsRequired().HasMaxLength(50);
                entity.Property(ai => ai.DeviceInfo).HasMaxLength(500);
                entity.Property(ai => ai.Notes).HasMaxLength(1000);
            });

            // Seed default data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Create default developer user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "amrali",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("braa"), // Change this password!
                    FullName = "عمرو علي",
                    Role = UserRole.Developer,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            );

            // Create default permissions for developer (all permissions enabled)
            modelBuilder.Entity<UserPermissions>().HasData(
                new UserPermissions
                {
                    UserPermissionsId = 1,
                    UserId = 1,
                    CanAddCar = true,
                    CanEditCar = true,
                    CanDeleteCar = true,
                    CanViewInventory = true,
                    CanSell = true,
                    CanEditSale = true,
                    CanDeleteSale = true,
                    CanViewSales = true,
                    CanAddCustomer = true,
                    CanEditCustomer = true,
                    CanDeleteCustomer = true,
                    CanViewCustomers = true,
                    CanViewCustomerReport = true,
                    CanManageSuppliers = true,
                    CanViewAccounts = true,
                    CanViewGeneralReports = true,
                    CanManageUsers = true,
                    CanManageSettings = true,
                    // Developer-specific permissions
                    CanAddManager = true,
                    CanManageManagerPassword = true,
                    CanActivateSubscription = true,
                    CanResetSystem = true,
                    CanRestoreDefaults = true,
                    // Manager permissions (developer has all)
                    CanFullActivityManagement = true,
                    CanCopyDatabase = true,
                    CanArchiveSystem = true,
                    CanAddSalesRep = true,
                    CanManageSalesRepPassword = true,
                    // Printing permissions
                    CanPrintReports = true,
                    CanPrintStatements = true,
                    // Installation permissions
                    CanActivateInstallation = true,
                    CreatedDate = DateTime.Now
                }
            );

            // Create default system settings
            modelBuilder.Entity<SystemSettings>().HasData(
                new SystemSettings
                {
                    SettingsId = 1,
                    SubscriptionType = SubscriptionType.Yearly,
                    SubscriptionStartDate = DateTime.Now,
                    SubscriptionEndDate = DateTime.Now.AddYears(1),
                    IsActive = true,
                    Currency = "USD",
                    CompanyName = "معرض السيارات",
                    CompanyAddress = "",
                    CompanyPhone = "",
                    CommercialRegister = "",
                    TaxCard = "",
                    AutoBackupEnabled = false,
                    BackupIntervalHours = 24,
                    BackupPath = "",
                    CreatedDate = DateTime.Now
                }
            );
        }
    }
}


